import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../widgets/custom_app_bar.dart';
import '../../theme/app_theme.dart';
import '../../services/auth_service.dart';
import '../../services/trial_service.dart';
import '../../services/storage_service.dart';
import '../../navigation/app_router.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _nameController = TextEditingController();
  final _goalController = TextEditingController();
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _goalController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    final user = AuthService.getCurrentUser();
    if (user != null) {
      _nameController.text = user.name;
      _goalController.text = user.monthlySavingsGoal.toStringAsFixed(0);
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.getCurrentUser();

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Profile',
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppSpacing.lg),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.primaryGreen.withOpacity(0.1),
                    AppTheme.accentGreen.withOpacity(0.05),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // Profile picture
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 50,
                        backgroundColor: AppTheme.primaryGreen.withOpacity(0.2),
                        child: user?.profilePictureUrl != null
                            ? ClipOval(
                                child: Image.network(
                                  user!.profilePictureUrl!,
                                  width: 100,
                                  height: 100,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) =>
                                      _buildDefaultAvatar(user.name),
                                ),
                              )
                            : _buildDefaultAvatar(user?.name ?? 'User'),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppTheme.primaryGreen,
                            borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                          ),
                          child: IconButton(
                            onPressed: _changeProfilePicture,
                            icon: const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 20,
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 36,
                              minHeight: 36,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // User name
                  if (_isEditing)
                    SizedBox(
                      width: 200,
                      child: TextFormField(
                        controller: _nameController,
                        textAlign: TextAlign.center,
                        style: AppTextStyles.heading2,
                        decoration: const InputDecoration(
                          border: UnderlineInputBorder(),
                          isDense: true,
                        ),
                      ),
                    )
                  else
                    Text(
                      user?.name ?? 'User',
                      style: AppTextStyles.heading2.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                  const SizedBox(height: AppSpacing.xs),

                  Text(
                    user?.email ?? '',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // Edit/Save button
                  TextButton.icon(
                    onPressed: _isEditing ? _saveProfile : _toggleEdit,
                    icon: Icon(_isEditing ? Icons.save : Icons.edit),
                    label: Text(_isEditing ? 'Save Changes' : 'Edit Profile'),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Settings sections
            _buildSettingsSection(
              'Account Settings',
              [
                _buildSettingsTile(
                  icon: Icons.savings,
                  title: 'Monthly Savings Goal',
                  subtitle: '\$${user?.monthlySavingsGoal.toStringAsFixed(0) ?? '0'}/month',
                  onTap: _editSavingsGoal,
                ),
                _buildSettingsTile(
                  icon: Icons.notifications,
                  title: 'Notification Settings',
                  subtitle: 'Manage your reminders',
                  onTap: _openNotificationSettings,
                ),
                _buildSettingsTile(
                  icon: Icons.security,
                  title: 'Change Password',
                  subtitle: 'Update your password',
                  onTap: _changePassword,
                ),
              ],
            ),

            _buildSettingsSection(
              'App Settings',
              [
                _buildSettingsTile(
                  icon: Icons.dark_mode,
                  title: 'Dark Mode',
                  subtitle: 'Toggle dark theme',
                  trailing: Switch(
                    value: Theme.of(context).brightness == Brightness.dark,
                    onChanged: _toggleDarkMode,
                  ),
                ),
                _buildSettingsTile(
                  icon: Icons.download,
                  title: 'Export Data',
                  subtitle: 'Download your trial data',
                  onTap: _exportData,
                ),
                _buildSettingsTile(
                  icon: Icons.backup,
                  title: 'Backup & Sync',
                  subtitle: 'Manage data backup',
                  onTap: _manageBackup,
                ),
              ],
            ),

            _buildSettingsSection(
              'Support & Info',
              [
                _buildSettingsTile(
                  icon: Icons.help,
                  title: 'Help Center',
                  subtitle: 'Get help and support',
                  onTap: _openHelpCenter,
                ),
                _buildSettingsTile(
                  icon: Icons.feedback,
                  title: 'Send Feedback',
                  subtitle: 'Help us improve the app',
                  onTap: _sendFeedback,
                ),
                _buildSettingsTile(
                  icon: Icons.star,
                  title: 'Rate the App',
                  subtitle: 'Leave a review',
                  onTap: _rateApp,
                ),
                _buildSettingsTile(
                  icon: Icons.info,
                  title: 'About',
                  subtitle: 'App version and info',
                  onTap: _showAbout,
                ),
              ],
            ),

            const SizedBox(height: AppSpacing.lg),

            // Sign out button
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
              child: SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _signOut,
                  icon: const Icon(Icons.logout, color: Colors.red),
                  label: const Text(
                    'Sign Out',
                    style: TextStyle(color: Colors.red),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                    padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
                  ),
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.xxl),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultAvatar(String name) {
    return Text(
      name.isNotEmpty ? name[0].toUpperCase() : 'U',
      style: AppTextStyles.heading1.copyWith(
        color: AppTheme.primaryGreen,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
          child: Text(
            title,
            style: AppTextStyles.heading3.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Card(
          margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
          child: Column(children: children),
        ),
        const SizedBox(height: AppSpacing.lg),
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppTheme.primaryGreen.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        child: Icon(
          icon,
          color: AppTheme.primaryGreen,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyLarge.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppTheme.textSecondary,
        ),
      ),
      trailing: trailing ?? (onTap != null ? const Icon(Icons.arrow_forward_ios, size: 16) : null),
      onTap: onTap,
    );
  }

  void _changeProfilePicture() {
    // TODO: Implement profile picture change
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Profile picture change coming soon!'),
      ),
    );
  }

  void _toggleEdit() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  Future<void> _saveProfile() async {
    if (_nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Name cannot be empty'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final result = await AuthService.updateProfile(
        name: _nameController.text.trim(),
      );

      if (result.isSuccess) {
        setState(() {
          _isEditing = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully!'),
              backgroundColor: AppTheme.successGreen,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'Failed to update profile'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _editSavingsGoal() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Monthly Savings Goal'),
        content: TextFormField(
          controller: _goalController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: const InputDecoration(
            labelText: 'Goal Amount',
            prefixText: '\$',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final goal = double.tryParse(_goalController.text);
              if (goal != null && goal > 0) {
                final user = AuthService.getCurrentUser();
                if (user != null) {
                  user.monthlySavingsGoal = goal;
                  await StorageService.updateUser(user);
                  setState(() {});
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Savings goal updated!'),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                }
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _openNotificationSettings() {
    // TODO: Navigate to notification settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notification settings coming soon!'),
      ),
    );
  }

  void _changePassword() {
    // TODO: Navigate to change password screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Change password coming soon!'),
      ),
    );
  }

  void _toggleDarkMode(bool value) {
    // TODO: Implement dark mode toggle
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Dark mode toggle coming soon!'),
      ),
    );
  }

  Future<void> _exportData() async {
    try {
      final data = await TrialService.exportTrialData();
      // TODO: Implement actual file export
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Data export feature coming soon!'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to export data: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _manageBackup() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Backup & sync coming soon!'),
      ),
    );
  }

  Future<void> _openHelpCenter() async {
    const url = 'https://help.trailio.app';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Could not open help center'),
        ),
      );
    }
  }

  Future<void> _sendFeedback() async {
    const email = '<EMAIL>';
    const subject = 'Trailio App Feedback';
    final url = 'mailto:$email?subject=$subject';

    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Could not open email app'),
        ),
      );
    }
  }

  Future<void> _rateApp() async {
    // TODO: Implement app store rating
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('App rating coming soon!'),
      ),
    );
  }

  void _showAbout() {
    showAboutDialog(
      context: context,
      applicationName: 'Trailio',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: AppTheme.primaryGreen.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        child: const Icon(
          Icons.savings,
          color: AppTheme.primaryGreen,
          size: 32,
        ),
      ),
      children: [
        const Text('Never miss a trial cancellation again!'),
        const SizedBox(height: AppSpacing.md),
        const Text('Track your free trials, get timely reminders, and save money by avoiding unwanted subscriptions.'),
      ],
    );
  }

  Future<void> _signOut() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      AppNavigation.signOut(context);
    }
  }
}
