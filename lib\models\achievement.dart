import 'package:hive/hive.dart';

part 'achievement.g.dart';

@HiveType(typeId: 5)
class Achievement extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String title;

  @HiveField(2)
  late String description;

  @HiveField(3)
  late String iconName;

  @HiveField(4)
  late AchievementCategory category;

  @HiveField(5)
  late int targetValue;

  @HiveField(6)
  late int currentValue;

  @HiveField(7)
  late bool isUnlocked;

  @HiveField(8)
  late DateTime? unlockedAt;

  @HiveField(9)
  late int rewardPoints;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.iconName,
    required this.category,
    required this.targetValue,
    this.currentValue = 0,
    this.isUnlocked = false,
    this.unlockedAt,
    this.rewardPoints = 10,
  });

  double get progress => targetValue > 0 ? (currentValue / targetValue).clamp(0.0, 1.0) : 0.0;

  bool get isCompleted => currentValue >= targetValue;

  void updateProgress(int value) {
    currentValue = value;
    if (!isUnlocked && isCompleted) {
      unlock();
    }
  }

  void unlock() {
    isUnlocked = true;
    unlockedAt = DateTime.now();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'iconName': iconName,
      'category': category.name,
      'targetValue': targetValue,
      'currentValue': currentValue,
      'isUnlocked': isUnlocked,
      'unlockedAt': unlockedAt?.toIso8601String(),
      'rewardPoints': rewardPoints,
    };
  }

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      iconName: json['iconName'],
      category: AchievementCategory.values.firstWhere((e) => e.name == json['category']),
      targetValue: json['targetValue'],
      currentValue: json['currentValue'] ?? 0,
      isUnlocked: json['isUnlocked'] ?? false,
      unlockedAt: json['unlockedAt'] != null ? DateTime.parse(json['unlockedAt']) : null,
      rewardPoints: json['rewardPoints'] ?? 10,
    );
  }
}

@HiveType(typeId: 6)
enum AchievementCategory {
  @HiveField(0)
  savings,
  @HiveField(1)
  trials,
  @HiveField(2)
  streaks,
  @HiveField(3)
  milestones,
}

class AchievementData {
  static List<Achievement> getDefaultAchievements() {
    return [
      // Savings achievements
      Achievement(
        id: 'first_save',
        title: 'First Save',
        description: 'Cancel your first trial and save money',
        iconName: 'savings',
        category: AchievementCategory.savings,
        targetValue: 1,
        rewardPoints: 25,
      ),
      Achievement(
        id: 'save_50',
        title: 'Smart Saver',
        description: 'Save \$50 in total',
        iconName: 'attach_money',
        category: AchievementCategory.savings,
        targetValue: 50,
        rewardPoints: 50,
      ),
      Achievement(
        id: 'save_100',
        title: 'Money Master',
        description: 'Save \$100 in total',
        iconName: 'account_balance_wallet',
        category: AchievementCategory.savings,
        targetValue: 100,
        rewardPoints: 100,
      ),
      Achievement(
        id: 'save_500',
        title: 'Savings Champion',
        description: 'Save \$500 in total',
        iconName: 'star',
        category: AchievementCategory.savings,
        targetValue: 500,
        rewardPoints: 250,
      ),

      // Trial achievements
      Achievement(
        id: 'first_trial',
        title: 'Trial Tracker',
        description: 'Add your first trial',
        iconName: 'add_circle',
        category: AchievementCategory.trials,
        targetValue: 1,
        rewardPoints: 15,
      ),
      Achievement(
        id: 'trial_5',
        title: 'Trial Collector',
        description: 'Track 5 trials',
        iconName: 'subscriptions',
        category: AchievementCategory.trials,
        targetValue: 5,
        rewardPoints: 30,
      ),
      Achievement(
        id: 'trial_10',
        title: 'Trial Expert',
        description: 'Track 10 trials',
        iconName: 'workspace_premium',
        category: AchievementCategory.trials,
        targetValue: 10,
        rewardPoints: 75,
      ),

      // Streak achievements
      Achievement(
        id: 'streak_7',
        title: 'Week Warrior',
        description: 'Use the app for 7 consecutive days',
        iconName: 'local_fire_department',
        category: AchievementCategory.streaks,
        targetValue: 7,
        rewardPoints: 50,
      ),
      Achievement(
        id: 'streak_30',
        title: 'Monthly Master',
        description: 'Use the app for 30 consecutive days',
        iconName: 'whatshot',
        category: AchievementCategory.streaks,
        targetValue: 30,
        rewardPoints: 150,
      ),

      // Milestone achievements
      Achievement(
        id: 'goal_achieved',
        title: 'Goal Getter',
        description: 'Achieve your monthly savings goal',
        iconName: 'emoji_events',
        category: AchievementCategory.milestones,
        targetValue: 1,
        rewardPoints: 100,
      ),
      Achievement(
        id: 'perfect_month',
        title: 'Perfect Month',
        description: 'Cancel all trials before they expire in a month',
        iconName: 'celebration',
        category: AchievementCategory.milestones,
        targetValue: 1,
        rewardPoints: 200,
      ),
    ];
  }

  static String getCategoryName(AchievementCategory category) {
    switch (category) {
      case AchievementCategory.savings:
        return 'Savings';
      case AchievementCategory.trials:
        return 'Trials';
      case AchievementCategory.streaks:
        return 'Streaks';
      case AchievementCategory.milestones:
        return 'Milestones';
    }
  }

  static String getCategoryIcon(AchievementCategory category) {
    switch (category) {
      case AchievementCategory.savings:
        return '💰';
      case AchievementCategory.trials:
        return '📱';
      case AchievementCategory.streaks:
        return '🔥';
      case AchievementCategory.milestones:
        return '🏆';
    }
  }
}
