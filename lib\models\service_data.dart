class ServiceCategory {
  final String id;
  final String name;
  final String icon;

  const ServiceCategory({
    required this.id,
    required this.name,
    required this.icon,
  });
}

class PopularService {
  final String name;
  final String category;
  final double typicalMonthlyCost;
  final int commonTrialDays;
  final String logoAsset;
  final String? websiteUrl;

  const PopularService({
    required this.name,
    required this.category,
    required this.typicalMonthlyCost,
    required this.commonTrialDays,
    required this.logoAsset,
    this.websiteUrl,
  });
}

class ServiceData {
  static const List<ServiceCategory> categories = [
    ServiceCategory(id: 'streaming', name: 'Streaming', icon: '📺'),
    ServiceCategory(id: 'music', name: 'Music', icon: '🎵'),
    ServiceCategory(id: 'productivity', name: 'Productivity', icon: '💼'),
    ServiceCategory(id: 'design', name: 'Design', icon: '🎨'),
    ServiceCategory(id: 'fitness', name: 'Fitness', icon: '💪'),
    ServiceCategory(id: 'education', name: 'Education', icon: '📚'),
    ServiceCategory(id: 'news', name: 'News', icon: '📰'),
    ServiceCategory(id: 'gaming', name: 'Gaming', icon: '🎮'),
    ServiceCategory(id: 'cloud', name: 'Cloud Storage', icon: '☁️'),
    ServiceCategory(id: 'other', name: 'Other', icon: '📱'),
  ];

  static const List<PopularService> popularServices = [
    // Streaming
    PopularService(
      name: 'Netflix',
      category: 'streaming',
      typicalMonthlyCost: 15.49,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/netflix.png',
      websiteUrl: 'https://netflix.com',
    ),
    PopularService(
      name: 'Disney+',
      category: 'streaming',
      typicalMonthlyCost: 7.99,
      commonTrialDays: 7,
      logoAsset: 'assets/logos/disney.png',
      websiteUrl: 'https://disneyplus.com',
    ),
    PopularService(
      name: 'Hulu',
      category: 'streaming',
      typicalMonthlyCost: 7.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/hulu.png',
      websiteUrl: 'https://hulu.com',
    ),
    PopularService(
      name: 'Amazon Prime Video',
      category: 'streaming',
      typicalMonthlyCost: 8.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/prime.png',
      websiteUrl: 'https://primevideo.com',
    ),
    PopularService(
      name: 'HBO Max',
      category: 'streaming',
      typicalMonthlyCost: 15.99,
      commonTrialDays: 7,
      logoAsset: 'assets/logos/hbo.png',
      websiteUrl: 'https://hbomax.com',
    ),

    // Music
    PopularService(
      name: 'Spotify Premium',
      category: 'music',
      typicalMonthlyCost: 9.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/spotify.png',
      websiteUrl: 'https://spotify.com',
    ),
    PopularService(
      name: 'Apple Music',
      category: 'music',
      typicalMonthlyCost: 9.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/apple_music.png',
      websiteUrl: 'https://music.apple.com',
    ),
    PopularService(
      name: 'YouTube Music',
      category: 'music',
      typicalMonthlyCost: 9.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/youtube_music.png',
      websiteUrl: 'https://music.youtube.com',
    ),

    // Productivity
    PopularService(
      name: 'Microsoft 365',
      category: 'productivity',
      typicalMonthlyCost: 6.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/microsoft365.png',
      websiteUrl: 'https://microsoft.com/microsoft-365',
    ),
    PopularService(
      name: 'Google Workspace',
      category: 'productivity',
      typicalMonthlyCost: 6.00,
      commonTrialDays: 14,
      logoAsset: 'assets/logos/google_workspace.png',
      websiteUrl: 'https://workspace.google.com',
    ),
    PopularService(
      name: 'Notion',
      category: 'productivity',
      typicalMonthlyCost: 8.00,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/notion.png',
      websiteUrl: 'https://notion.so',
    ),
    PopularService(
      name: 'Slack',
      category: 'productivity',
      typicalMonthlyCost: 7.25,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/slack.png',
      websiteUrl: 'https://slack.com',
    ),

    // Design
    PopularService(
      name: 'Adobe Creative Cloud',
      category: 'design',
      typicalMonthlyCost: 52.99,
      commonTrialDays: 7,
      logoAsset: 'assets/logos/adobe.png',
      websiteUrl: 'https://adobe.com',
    ),
    PopularService(
      name: 'Canva Pro',
      category: 'design',
      typicalMonthlyCost: 12.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/canva.png',
      websiteUrl: 'https://canva.com',
    ),
    PopularService(
      name: 'Figma',
      category: 'design',
      typicalMonthlyCost: 12.00,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/figma.png',
      websiteUrl: 'https://figma.com',
    ),

    // Fitness
    PopularService(
      name: 'Peloton',
      category: 'fitness',
      typicalMonthlyCost: 12.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/peloton.png',
      websiteUrl: 'https://onepeloton.com',
    ),
    PopularService(
      name: 'MyFitnessPal Premium',
      category: 'fitness',
      typicalMonthlyCost: 9.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/myfitnesspal.png',
      websiteUrl: 'https://myfitnesspal.com',
    ),

    // Education
    PopularService(
      name: 'MasterClass',
      category: 'education',
      typicalMonthlyCost: 15.00,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/masterclass.png',
      websiteUrl: 'https://masterclass.com',
    ),
    PopularService(
      name: 'Coursera Plus',
      category: 'education',
      typicalMonthlyCost: 49.00,
      commonTrialDays: 7,
      logoAsset: 'assets/logos/coursera.png',
      websiteUrl: 'https://coursera.org',
    ),

    // Gaming
    PopularService(
      name: 'Xbox Game Pass',
      category: 'gaming',
      typicalMonthlyCost: 14.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/xbox.png',
      websiteUrl: 'https://xbox.com/game-pass',
    ),
    PopularService(
      name: 'PlayStation Plus',
      category: 'gaming',
      typicalMonthlyCost: 9.99,
      commonTrialDays: 7,
      logoAsset: 'assets/logos/playstation.png',
      websiteUrl: 'https://playstation.com/ps-plus',
    ),

    // Cloud Storage
    PopularService(
      name: 'Dropbox Plus',
      category: 'cloud',
      typicalMonthlyCost: 9.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/dropbox.png',
      websiteUrl: 'https://dropbox.com',
    ),
    PopularService(
      name: 'Google One',
      category: 'cloud',
      typicalMonthlyCost: 1.99,
      commonTrialDays: 30,
      logoAsset: 'assets/logos/google_one.png',
      websiteUrl: 'https://one.google.com',
    ),
  ];

  static String getCategoryName(String categoryId) {
    return categories.firstWhere(
      (cat) => cat.id == categoryId,
      orElse: () => const ServiceCategory(id: 'other', name: 'Other', icon: '📱'),
    ).name;
  }

  static String getCategoryIcon(String categoryId) {
    return categories.firstWhere(
      (cat) => cat.id == categoryId,
      orElse: () => const ServiceCategory(id: 'other', name: 'Other', icon: '📱'),
    ).icon;
  }

  static PopularService? getServiceByName(String name) {
    try {
      return popularServices.firstWhere((service) => service.name.toLowerCase() == name.toLowerCase());
    } catch (e) {
      return null;
    }
  }

  static List<PopularService> getServicesByCategory(String categoryId) {
    return popularServices.where((service) => service.category == categoryId).toList();
  }
}
