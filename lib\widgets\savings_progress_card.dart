import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class SavingsProgressCard extends StatelessWidget {
  final double currentSavings;
  final double goalAmount;
  final String currency;
  final VoidCallback? onTap;

  const SavingsProgressCard({
    super.key,
    required this.currentSavings,
    required this.goalAmount,
    this.currency = 'USD',
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final progress = goalAmount > 0 ? (currentSavings / goalAmount).clamp(0.0, 1.0) : 0.0;
    final isGoalAchieved = currentSavings >= goalAmount;

    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.xl),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppBorderRadius.xl),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isGoalAchieved
                  ? [
                      AppTheme.successGreen.withValues(alpha: 0.1),
                      AppTheme.celebrationGold.withValues(alpha: 0.1),
                    ]
                  : [
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.08),
                      Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
                    ],
            ),
            border: Border.all(
              color: isGoalAchieved
                  ? AppTheme.successGreen.withValues(alpha: 0.2)
                  : Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.md),
                    decoration: BoxDecoration(
                      color: isGoalAchieved
                          ? AppTheme.celebrationGold.withValues(alpha: 0.2)
                          : Theme.of(context).colorScheme.primary.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                    ),
                    child: Icon(
                      isGoalAchieved ? Icons.celebration_rounded : Icons.savings_rounded,
                      color: isGoalAchieved ? AppTheme.celebrationGold : Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isGoalAchieved ? 'Goal Achieved! 🎉' : 'Monthly Savings Goal',
                          style: AppTextStyles.heading3Primary(context).copyWith(
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          isGoalAchieved
                              ? 'Congratulations on reaching your goal!'
                              : 'Track your progress towards financial freedom',
                          style: AppTextStyles.bodySmallSecondary(context),
                        ),
                      ],
                    ),
                  ),
                  if (onTap != null)
                    Container(
                      padding: const EdgeInsets.all(AppSpacing.sm),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(AppBorderRadius.md),
                      ),
                      child: Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: 16,
                        color: AppTheme.getTextSecondary(context),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.lg),
              
              // Savings amount
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '\$${currentSavings.toStringAsFixed(0)}',
                    style: AppTextStyles.heading1.copyWith(
                      color: isGoalAchieved ? AppTheme.successGreen : AppTheme.primaryGreen,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      'of \$${goalAmount.toStringAsFixed(0)}',
                      style: AppTextStyles.bodyLargeSecondary(context),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.md),
              
              // Progress bar
              Container(
                padding: const EdgeInsets.all(AppSpacing.lg),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${(progress * 100).toStringAsFixed(0)}% Complete',
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w700,
                            color: isGoalAchieved ? AppTheme.successGreen : Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        if (goalAmount > currentSavings)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSpacing.md,
                              vertical: AppSpacing.xs,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                            ),
                            child: Text(
                              '\$${(goalAmount - currentSavings).toStringAsFixed(0)} to go',
                              style: AppTextStyles.bodySmall.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: AppSpacing.md),
                    Container(
                      height: 12,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                        child: LinearProgressIndicator(
                          value: progress,
                          backgroundColor: Colors.transparent,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            isGoalAchieved ? AppTheme.successGreen : Theme.of(context).colorScheme.primary,
                          ),
                          minHeight: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              if (isGoalAchieved) ...[
                const SizedBox(height: AppSpacing.md),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.md,
                    vertical: AppSpacing.sm,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.successGreen.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                    border: Border.all(
                      color: AppTheme.successGreen.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: AppTheme.successGreen,
                        size: 20,
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Expanded(
                        child: Text(
                          'Congratulations! You\'ve reached your savings goal this month.',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppTheme.successGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
