import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/trial.dart';
import '../theme/app_theme.dart';

class TrialCard extends StatelessWidget {
  final Trial trial;
  final VoidCallback? onTap;
  final VoidCallback? onCancel;
  final VoidCallback? onSnooze;

  const TrialCard({
    super.key,
    required this.trial,
    this.onTap,
    this.onCancel,
    this.onSnooze,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppBorderRadius.xl),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Service logo/icon
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                      color: _getUrgencyColor().withValues(alpha: 0.1),
                      border: Border.all(
                        color: _getUrgencyColor().withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: trial.logoUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                            child: Image.network(
                              trial.logoUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => _buildDefaultIcon(),
                            ),
                          )
                        : _buildDefaultIcon(),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  
                  // Service info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          trial.serviceName,
                          style: AppTextStyles.heading3Primary(context).copyWith(
                            fontWeight: FontWeight.w700,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpacing.sm,
                            vertical: AppSpacing.xs,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                            ),
                          ),
                          child: Text(
                            _getCategoryDisplayName(),
                            style: AppTextStyles.bodySmall.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Urgency indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.md,
                      vertical: AppSpacing.sm,
                    ),
                    decoration: BoxDecoration(
                      color: _getUrgencyColor(),
                      borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                      boxShadow: [
                        BoxShadow(
                          color: _getUrgencyColor().withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text(
                      _getUrgencyText(),
                      style: AppTextStyles.caption.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        fontSize: 11,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.md),
              
              // Trial details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      icon: Icons.calendar_today_rounded,
                      label: 'Expires',
                      value: _getExpiryText(),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      icon: Icons.attach_money_rounded,
                      label: 'Monthly Cost',
                      value: '\$${trial.monthlyCost.toStringAsFixed(2)}',
                    ),
                  ),
                ],
              ),
              
              if (trial.status == TrialStatus.active) ...[
                const SizedBox(height: AppSpacing.lg),

                // Action buttons
                Container(
                  padding: const EdgeInsets.all(AppSpacing.md),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: onSnooze,
                          icon: const Icon(Icons.snooze_rounded, size: 18),
                          label: const Text('Snooze'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
                            side: BorderSide(
                              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppSpacing.md),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton.icon(
                          onPressed: onCancel,
                          icon: const Icon(Icons.cancel_rounded, size: 18),
                          label: const Text('Cancel Trial'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _getUrgencyColor(),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
                            elevation: 2,
                            shadowColor: _getUrgencyColor().withValues(alpha: 0.3),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultIcon() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Icon(
        Icons.subscriptions_rounded,
        color: _getUrgencyColor(),
        size: 28,
      ),
    );
  }

  Widget _buildDetailItem(BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
            ),
            child: Icon(
              icon,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTextStyles.bodySmallSecondary(context),
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  value,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppTheme.getTextPrimary(context),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getUrgencyColor() {
    switch (trial.urgencyLevel) {
      case UrgencyLevel.high:
        return AppTheme.urgentRed;
      case UrgencyLevel.medium:
        return AppTheme.warningOrange;
      case UrgencyLevel.low:
        return AppTheme.cautionYellow;
      case UrgencyLevel.none:
        return AppTheme.primaryGreen;
    }
  }

  String _getUrgencyText() {
    if (trial.status != TrialStatus.active) {
      return trial.status.name.toUpperCase();
    }

    final daysRemaining = trial.daysRemaining;
    if (daysRemaining < 0) {
      return 'EXPIRED';
    } else if (daysRemaining == 0) {
      return 'TODAY';
    } else if (daysRemaining == 1) {
      return '1 DAY';
    } else {
      return '$daysRemaining DAYS';
    }
  }

  String _getExpiryText() {
    if (trial.status != TrialStatus.active) {
      switch (trial.status) {
        case TrialStatus.cancelled:
          return trial.cancelledDate != null
              ? DateFormat('MMM d, y').format(trial.cancelledDate!)
              : 'Cancelled';
        case TrialStatus.expired:
          return 'Expired';
        case TrialStatus.converted:
          return 'Converted';
        default:
          return '';
      }
    }

    final daysRemaining = trial.daysRemaining;
    if (daysRemaining < 0) {
      return 'Expired';
    } else if (daysRemaining == 0) {
      return 'Today';
    } else if (daysRemaining == 1) {
      return 'Tomorrow';
    } else {
      return DateFormat('MMM d, y').format(trial.expiryDate);
    }
  }

  String _getCategoryDisplayName() {
    return trial.serviceCategory.split('_').map((word) {
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }
}
