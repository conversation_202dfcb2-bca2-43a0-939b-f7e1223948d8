import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../widgets/custom_app_bar.dart';
import '../../theme/app_theme.dart';
import '../../models/service_data.dart';
import '../../services/trial_service.dart';

class AddTrialScreen extends StatefulWidget {
  const AddTrialScreen({super.key});

  @override
  State<AddTrialScreen> createState() => _AddTrialScreenState();
}

class _AddTrialScreenState extends State<AddTrialScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _serviceNameController = TextEditingController();
  final _monthlyCostController = TextEditingController();
  final _notesController = TextEditingController();

  // Form state
  String _selectedCategory = 'streaming';
  DateTime _startDate = DateTime.now();
  int _trialDuration = 30;
  List<int> _reminderDays = [1, 3, 7];
  PopularService? _selectedService;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _serviceNameController.dispose();
    _monthlyCostController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Add Trial',
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Popular Services'),
            Tab(text: 'Custom Service'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPopularServicesTab(),
          _buildCustomServiceTab(),
        ],
      ),
    );
  }

  Widget _buildPopularServicesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search bar
          TextField(
            decoration: InputDecoration(
              hintText: 'Search services...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppBorderRadius.md),
              ),
            ),
            onChanged: (value) {
              // TODO: Implement search functionality
            },
          ),

          const SizedBox(height: AppSpacing.lg),

          // Categories
          Text(
            'Categories',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppSpacing.md),

          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: ServiceData.categories.length,
              itemBuilder: (context, index) {
                final category = ServiceData.categories[index];
                final isSelected = _selectedCategory == category.id;

                return Padding(
                  padding: const EdgeInsets.only(right: AppSpacing.sm),
                  child: FilterChip(
                    label: Text('${category.icon} ${category.name}'),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category.id;
                      });
                    },
                    selectedColor: AppTheme.primaryGreen.withOpacity(0.2),
                    checkmarkColor: AppTheme.primaryGreen,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: AppSpacing.lg),

          // Popular services grid
          Text(
            'Popular Services',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppSpacing.md),

          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.2,
              crossAxisSpacing: AppSpacing.md,
              mainAxisSpacing: AppSpacing.md,
            ),
            itemCount: ServiceData.getServicesByCategory(_selectedCategory).length,
            itemBuilder: (context, index) {
              final service = ServiceData.getServicesByCategory(_selectedCategory)[index];

              return Card(
                child: InkWell(
                  onTap: () => _selectService(service),
                  borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                  child: Padding(
                    padding: const EdgeInsets.all(AppSpacing.md),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Service logo placeholder
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: AppTheme.primaryGreen.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(AppBorderRadius.md),
                          ),
                          child: const Icon(
                            Icons.subscriptions,
                            color: AppTheme.primaryGreen,
                          ),
                        ),

                        const SizedBox(height: AppSpacing.sm),

                        Text(
                          service.name,
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: AppSpacing.xs),

                        Text(
                          '\$${service.typicalMonthlyCost.toStringAsFixed(2)}/mo',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCustomServiceTab() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Service name
            TextFormField(
              controller: _serviceNameController,
              decoration: const InputDecoration(
                labelText: 'Service Name *',
                hintText: 'e.g., Netflix, Spotify, Adobe',
                prefixIcon: Icon(Icons.business),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a service name';
                }
                return null;
              },
            ),

            const SizedBox(height: AppSpacing.md),

            // Category selection
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                prefixIcon: Icon(Icons.category),
              ),
              items: ServiceData.categories.map((category) {
                return DropdownMenuItem(
                  value: category.id,
                  child: Text('${category.icon} ${category.name}'),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
            ),

            const SizedBox(height: AppSpacing.md),

            // Start date
            InkWell(
              onTap: _selectStartDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Trial Start Date',
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  DateFormat('MMM d, y').format(_startDate),
                  style: AppTextStyles.bodyMedium,
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.md),

            // Trial duration
            DropdownButtonFormField<int>(
              value: _trialDuration,
              decoration: const InputDecoration(
                labelText: 'Trial Duration',
                prefixIcon: Icon(Icons.timer),
              ),
              items: const [
                DropdownMenuItem(value: 7, child: Text('7 days')),
                DropdownMenuItem(value: 14, child: Text('14 days')),
                DropdownMenuItem(value: 30, child: Text('30 days')),
                DropdownMenuItem(value: 60, child: Text('60 days')),
                DropdownMenuItem(value: 90, child: Text('90 days')),
              ],
              onChanged: (value) {
                setState(() {
                  _trialDuration = value!;
                });
              },
            ),

            const SizedBox(height: AppSpacing.md),

            // Monthly cost
            TextFormField(
              controller: _monthlyCostController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              decoration: const InputDecoration(
                labelText: 'Monthly Cost After Trial *',
                hintText: '0.00',
                prefixIcon: Icon(Icons.attach_money),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter the monthly cost';
                }
                final cost = double.tryParse(value);
                if (cost == null || cost <= 0) {
                  return 'Please enter a valid cost';
                }
                return null;
              },
            ),

            const SizedBox(height: AppSpacing.lg),

            // Reminder settings
            Text(
              'Reminder Settings',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppSpacing.md),

            Text(
              'Get notified before your trial expires:',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),

            Wrap(
              spacing: AppSpacing.sm,
              children: [1, 3, 7, 14].map((days) {
                final isSelected = _reminderDays.contains(days);
                return FilterChip(
                  label: Text('$days day${days == 1 ? '' : 's'} before'),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _reminderDays.add(days);
                      } else {
                        _reminderDays.remove(days);
                      }
                      _reminderDays.sort();
                    });
                  },
                  selectedColor: AppTheme.primaryGreen.withOpacity(0.2),
                  checkmarkColor: AppTheme.primaryGreen,
                );
              }).toList(),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Notes
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                hintText: 'Add any additional notes about this trial...',
                prefixIcon: Icon(Icons.note),
                alignLabelWithHint: true,
              ),
            ),

            const SizedBox(height: AppSpacing.xl),

            // Add trial button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _addTrial,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Add Trial',
                        style: AppTextStyles.button,
                      ),
              ),
            ),

            const SizedBox(height: AppSpacing.xl),
          ],
        ),
      ),
    );
  }

  void _selectService(PopularService service) {
    setState(() {
      _selectedService = service;
      _serviceNameController.text = service.name;
      _selectedCategory = service.category;
      _trialDuration = service.commonTrialDays;
      _monthlyCostController.text = service.typicalMonthlyCost.toStringAsFixed(2);
    });

    // Switch to custom tab to show the form
    _tabController.animateTo(1);
  }

  Future<void> _selectStartDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked;
      });
    }
  }

  Future<void> _addTrial() async {
    if (!_formKey.currentState!.validate()) return;

    if (_reminderDays.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one reminder option'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await TrialService.createTrial(
        serviceName: _serviceNameController.text.trim(),
        serviceCategory: _selectedCategory,
        logoUrl: _selectedService?.logoAsset,
        startDate: _startDate,
        trialDurationDays: _trialDuration,
        monthlyCost: double.parse(_monthlyCostController.text),
        reminderDaysBeforeExpiry: _reminderDays,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${_serviceNameController.text} trial added successfully!'),
            backgroundColor: AppTheme.successGreen,
          ),
        );

        // Clear form
        _clearForm();

        // Navigate back to home
        context.go('/home');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add trial: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _clearForm() {
    _serviceNameController.clear();
    _monthlyCostController.clear();
    _notesController.clear();
    setState(() {
      _selectedService = null;
      _selectedCategory = 'streaming';
      _startDate = DateTime.now();
      _trialDuration = 30;
      _reminderDays = [1, 3, 7];
    });
  }
}
