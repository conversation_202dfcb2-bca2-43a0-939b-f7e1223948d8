import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/user.dart';
import 'storage_service.dart';

class AuthService {

  static Future<AuthResult> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      // Validate input
      final validation = _validateSignUpInput(email, password, name);
      if (!validation.isValid) {
        return AuthResult.failure(validation.error!);
      }

      // Check if user already exists
      final existingUser = await _getUserByEmail(email);
      if (existingUser != null) {
        return AuthResult.failure('An account with this email already exists');
      }

      // Create new user
      final user = User(
        id: const Uuid().v4(),
        email: email.toLowerCase().trim(),
        name: name.trim(),
        isEmailVerified: false, // Will be verified via email
      );

      // Hash password and store
      final hashedPassword = _hashPassword(password);
      await _storeUserPassword(user.id, hashedPassword);

      // Save user
      await StorageService.saveUser(user);

      // Generate email verification token
      await _generateEmailVerificationToken(user.email);

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Failed to create account: ${e.toString()}');
    }
  }

  static Future<AuthResult> signIn({
    required String email,
    required String password,
  }) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty) {
        return AuthResult.failure('Email and password are required');
      }

      // Find user
      final user = await _getUserByEmail(email);
      if (user == null) {
        return AuthResult.failure('Invalid email or password');
      }

      // Verify password
      final storedPasswordHash = await _getUserPassword(user.id);
      if (storedPasswordHash == null || !_verifyPassword(password, storedPasswordHash)) {
        return AuthResult.failure('Invalid email or password');
      }

      // Set as current user
      await StorageService.setCurrentUserId(user.id);

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Failed to sign in: ${e.toString()}');
    }
  }

  static Future<void> signOut() async {
    await StorageService.clearCurrentUser();
  }

  static Future<AuthResult> resetPassword({
    required String email,
  }) async {
    try {
      final user = await _getUserByEmail(email);
      if (user == null) {
        // Don't reveal if email exists for security
        return AuthResult.success(null, message: 'If an account with this email exists, a password reset link has been sent');
      }

      // Generate reset token
      await _generatePasswordResetToken(email);

      // In a real app, you would send this token via email
      // For now, we'll just store it and return success
      
      return AuthResult.success(null, message: 'Password reset instructions have been sent to your email');
    } catch (e) {
      return AuthResult.failure('Failed to send reset email: ${e.toString()}');
    }
  }

  static Future<AuthResult> confirmPasswordReset({
    required String token,
    required String newPassword,
  }) async {
    try {
      // Validate new password
      if (!_isValidPassword(newPassword)) {
        return AuthResult.failure('Password must be at least 8 characters long');
      }

      // Verify token
      final email = await _verifyPasswordResetToken(token);
      if (email == null) {
        return AuthResult.failure('Invalid or expired reset token');
      }

      // Find user
      final user = await _getUserByEmail(email);
      if (user == null) {
        return AuthResult.failure('User not found');
      }

      // Update password
      final hashedPassword = _hashPassword(newPassword);
      await _storeUserPassword(user.id, hashedPassword);

      // Clear reset token
      await _clearPasswordResetToken(token);

      return AuthResult.success(user, message: 'Password updated successfully');
    } catch (e) {
      return AuthResult.failure('Failed to reset password: ${e.toString()}');
    }
  }

  static Future<AuthResult> verifyEmail({
    required String token,
  }) async {
    try {
      // Verify token
      final email = await _verifyEmailVerificationToken(token);
      if (email == null) {
        return AuthResult.failure('Invalid or expired verification token');
      }

      // Find user
      final user = await _getUserByEmail(email);
      if (user == null) {
        return AuthResult.failure('User not found');
      }

      // Mark email as verified
      user.isEmailVerified = true;
      user.updatedAt = DateTime.now();
      await StorageService.updateUser(user);

      // Clear verification token
      await _clearEmailVerificationToken(token);

      return AuthResult.success(user, message: 'Email verified successfully');
    } catch (e) {
      return AuthResult.failure('Failed to verify email: ${e.toString()}');
    }
  }

  static Future<AuthResult> resendEmailVerification({
    required String email,
  }) async {
    try {
      final user = await _getUserByEmail(email);
      if (user == null) {
        return AuthResult.failure('User not found');
      }

      if (user.isEmailVerified) {
        return AuthResult.failure('Email is already verified');
      }

      // Generate new verification token
      await _generateEmailVerificationToken(email);

      return AuthResult.success(null, message: 'Verification email sent');
    } catch (e) {
      return AuthResult.failure('Failed to send verification email: ${e.toString()}');
    }
  }

  static User? getCurrentUser() {
    return StorageService.getCurrentUser();
  }

  static bool isSignedIn() {
    return getCurrentUser() != null;
  }

  static Future<AuthResult> updateProfile({
    required String name,
    String? profilePictureUrl,
  }) async {
    try {
      final user = getCurrentUser();
      if (user == null) {
        return AuthResult.failure('Not signed in');
      }

      user.name = name.trim();
      if (profilePictureUrl != null) {
        user.profilePictureUrl = profilePictureUrl;
      }
      user.updatedAt = DateTime.now();

      await StorageService.updateUser(user);

      return AuthResult.success(user, message: 'Profile updated successfully');
    } catch (e) {
      return AuthResult.failure('Failed to update profile: ${e.toString()}');
    }
  }

  static Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = getCurrentUser();
      if (user == null) {
        return AuthResult.failure('Not signed in');
      }

      // Verify current password
      final storedPasswordHash = await _getUserPassword(user.id);
      if (storedPasswordHash == null || !_verifyPassword(currentPassword, storedPasswordHash)) {
        return AuthResult.failure('Current password is incorrect');
      }

      // Validate new password
      if (!_isValidPassword(newPassword)) {
        return AuthResult.failure('New password must be at least 8 characters long');
      }

      // Update password
      final hashedPassword = _hashPassword(newPassword);
      await _storeUserPassword(user.id, hashedPassword);

      return AuthResult.success(user, message: 'Password changed successfully');
    } catch (e) {
      return AuthResult.failure('Failed to change password: ${e.toString()}');
    }
  }

  // Private helper methods
  static ValidationResult _validateSignUpInput(String email, String password, String name) {
    if (email.isEmpty || password.isEmpty || name.isEmpty) {
      return ValidationResult(false, 'All fields are required');
    }

    if (!_isValidEmail(email)) {
      return ValidationResult(false, 'Please enter a valid email address');
    }

    if (!_isValidPassword(password)) {
      return ValidationResult(false, 'Password must be at least 8 characters long');
    }

    if (name.trim().length < 2) {
      return ValidationResult(false, 'Name must be at least 2 characters long');
    }

    return ValidationResult(true, null);
  }

  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  static bool _isValidPassword(String password) {
    return password.length >= 8;
  }

  static String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  static bool _verifyPassword(String password, String hashedPassword) {
    return _hashPassword(password) == hashedPassword;
  }

  static Future<User?> _getUserByEmail(String email) async {
    // This is a simple implementation - in a real app you'd have a proper database
    // For now, we'll iterate through stored users
    final currentUser = StorageService.getCurrentUser();
    if (currentUser != null && currentUser.email.toLowerCase() == email.toLowerCase()) {
      return currentUser;
    }
    return null;
  }

  static Future<void> _storeUserPassword(String userId, String hashedPassword) async {
    // Store password hash securely (in a real app, this would be server-side)
    // For demo purposes, we'll use SharedPreferences with a key based on user ID
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('password_$userId', hashedPassword);
  }

  static Future<String?> _getUserPassword(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('password_$userId');
  }

  static Future<String> _generatePasswordResetToken(String email) async {
    final token = _generateSecureToken();
    final expiryTime = DateTime.now().add(const Duration(hours: 1));
    
    final prefs = await SharedPreferences.getInstance();
    final tokenData = {
      'email': email,
      'expiry': expiryTime.toIso8601String(),
    };
    await prefs.setString('reset_token_$token', jsonEncode(tokenData));
    
    return token;
  }

  static Future<String?> _verifyPasswordResetToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    final tokenDataString = prefs.getString('reset_token_$token');
    
    if (tokenDataString == null) return null;
    
    final tokenData = jsonDecode(tokenDataString);
    final expiry = DateTime.parse(tokenData['expiry']);
    
    if (DateTime.now().isAfter(expiry)) {
      await prefs.remove('reset_token_$token');
      return null;
    }
    
    return tokenData['email'];
  }

  static Future<void> _clearPasswordResetToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('reset_token_$token');
  }

  static Future<String> _generateEmailVerificationToken(String email) async {
    final token = _generateSecureToken();
    final expiryTime = DateTime.now().add(const Duration(days: 7));
    
    final prefs = await SharedPreferences.getInstance();
    final tokenData = {
      'email': email,
      'expiry': expiryTime.toIso8601String(),
    };
    await prefs.setString('verify_token_$token', jsonEncode(tokenData));
    
    return token;
  }

  static Future<String?> _verifyEmailVerificationToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    final tokenDataString = prefs.getString('verify_token_$token');
    
    if (tokenDataString == null) return null;
    
    final tokenData = jsonDecode(tokenDataString);
    final expiry = DateTime.parse(tokenData['expiry']);
    
    if (DateTime.now().isAfter(expiry)) {
      await prefs.remove('verify_token_$token');
      return null;
    }
    
    return tokenData['email'];
  }

  static Future<void> _clearEmailVerificationToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('verify_token_$token');
  }

  static String _generateSecureToken() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }
}

class AuthResult {
  final bool isSuccess;
  final User? user;
  final String? error;
  final String? message;

  AuthResult._(this.isSuccess, this.user, this.error, this.message);

  factory AuthResult.success(User? user, {String? message}) {
    return AuthResult._(true, user, null, message);
  }

  factory AuthResult.failure(String error) {
    return AuthResult._(false, null, error, null);
  }
}

class ValidationResult {
  final bool isValid;
  final String? error;

  ValidationResult(this.isValid, this.error);
}
