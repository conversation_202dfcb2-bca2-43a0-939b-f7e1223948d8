import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/trial_card.dart';
import '../../theme/app_theme.dart';
import '../../services/trial_service.dart';
import '../../models/trial.dart';
import '../../models/service_data.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  // Filter state
  String _selectedTimeRange = 'all';
  TrialStatus? _selectedStatus;
  String? _selectedCategory;

  // Data
  List<Trial> _filteredTrials = [];
  List<MonthlyData> _savingsData = [];
  Map<String, double> _savingsByCategory = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    setState(() {
      _filteredTrials = _getFilteredTrials();
      _savingsData = TrialService.getMonthlySavingsData(12);
      _savingsByCategory = TrialService.getSavingsByCategory();
    });
  }

  List<Trial> _getFilteredTrials() {
    var trials = TrialService.getTrialsByStatus(_selectedStatus ?? TrialStatus.cancelled);

    // Apply time range filter
    if (_selectedTimeRange != 'all') {
      final now = DateTime.now();
      DateTime startDate;

      switch (_selectedTimeRange) {
        case 'month':
          startDate = DateTime(now.year, now.month, 1);
          break;
        case '3months':
          startDate = DateTime(now.year, now.month - 3, 1);
          break;
        case 'year':
          startDate = DateTime(now.year, 1, 1);
          break;
        default:
          startDate = DateTime(2020, 1, 1);
      }

      trials = trials.where((trial) => trial.createdAt.isAfter(startDate)).toList();
    }

    // Apply category filter
    if (_selectedCategory != null) {
      trials = trials.where((trial) => trial.serviceCategory == _selectedCategory).toList();
    }

    // Sort by date (newest first)
    trials.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return trials;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'History & Analytics',
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Analytics'),
            Tab(text: 'History'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAnalyticsTab(),
          _buildHistoryTab(),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    final totalSavings = _savingsByCategory.values.fold(0.0, (sum, value) => sum + value);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Total savings card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.savings,
                        color: AppTheme.successGreen,
                        size: 32,
                      ),
                      const SizedBox(width: AppSpacing.md),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Total Savings',
                              style: AppTextStyles.bodyLarge.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                            ),
                            Text(
                              '\$${totalSavings.toStringAsFixed(2)}',
                              style: AppTextStyles.heading1.copyWith(
                                color: AppTheme.successGreen,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppSpacing.lg),

          // Monthly savings chart
          Text(
            'Monthly Savings Trend',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppSpacing.md),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: SizedBox(
                height: 200,
                child: _savingsData.isNotEmpty
                    ? LineChart(
                        LineChartData(
                          gridData: FlGridData(show: false),
                          titlesData: FlTitlesData(
                            leftTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                reservedSize: 40,
                                getTitlesWidget: (value, meta) {
                                  return Text(
                                    '\$${value.toInt()}',
                                    style: AppTextStyles.bodySmall,
                                  );
                                },
                              ),
                            ),
                            bottomTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                getTitlesWidget: (value, meta) {
                                  final index = value.toInt();
                                  if (index >= 0 && index < _savingsData.length) {
                                    return Text(
                                      DateFormat('MMM').format(_savingsData[index].month),
                                      style: AppTextStyles.bodySmall,
                                    );
                                  }
                                  return const Text('');
                                },
                              ),
                            ),
                            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                          ),
                          borderData: FlBorderData(show: false),
                          lineBarsData: [
                            LineChartBarData(
                              spots: _savingsData.asMap().entries.map((entry) {
                                return FlSpot(entry.key.toDouble(), entry.value.savings);
                              }).toList(),
                              isCurved: true,
                              color: AppTheme.primaryGreen,
                              barWidth: 3,
                              dotData: FlDotData(show: true),
                              belowBarData: BarAreaData(
                                show: true,
                                color: AppTheme.primaryGreen.withOpacity(0.1),
                              ),
                            ),
                          ],
                        ),
                      )
                    : Center(
                        child: Text(
                          'No data available',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ),
              ),
            ),
          ),

          const SizedBox(height: AppSpacing.lg),

          // Savings by category
          Text(
            'Savings by Category',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppSpacing.md),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: _savingsByCategory.isNotEmpty
                  ? Column(
                      children: _savingsByCategory.entries.map((entry) {
                        final percentage = totalSavings > 0 ? (entry.value / totalSavings) * 100 : 0;
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  entry.key.replaceAll('_', ' ').toUpperCase(),
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 3,
                                child: LinearProgressIndicator(
                                  value: percentage / 100,
                                  backgroundColor: Colors.grey.shade200,
                                  valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
                                ),
                              ),
                              const SizedBox(width: AppSpacing.sm),
                              Text(
                                '\$${entry.value.toStringAsFixed(0)}',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    )
                  : Center(
                      child: Padding(
                        padding: const EdgeInsets.all(AppSpacing.lg),
                        child: Text(
                          'No savings data available',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab() {
    return Column(
      children: [
        // Filters
        Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              bottom: BorderSide(
                color: Colors.grey.shade200,
                width: 1,
              ),
            ),
          ),
          child: Column(
            children: [
              // Time range filter
              Row(
                children: [
                  Text(
                    'Time Range:',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          _buildFilterChip('All Time', 'all'),
                          _buildFilterChip('This Month', 'month'),
                          _buildFilterChip('Last 3 Months', '3months'),
                          _buildFilterChip('This Year', 'year'),
                        ],
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppSpacing.sm),

              // Status and category filters
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<TrialStatus?>(
                      value: _selectedStatus,
                      decoration: const InputDecoration(
                        labelText: 'Status',
                        isDense: true,
                      ),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('All Statuses'),
                        ),
                        ...TrialStatus.values.map((status) {
                          return DropdownMenuItem(
                            value: status,
                            child: Text(status.name.toUpperCase()),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedStatus = value;
                          _loadData();
                        });
                      },
                    ),
                  ),

                  const SizedBox(width: AppSpacing.md),

                  Expanded(
                    child: DropdownButtonFormField<String?>(
                      value: _selectedCategory,
                      decoration: const InputDecoration(
                        labelText: 'Category',
                        isDense: true,
                      ),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('All Categories'),
                        ),
                        ...ServiceData.categories.map((category) {
                          return DropdownMenuItem(
                            value: category.id,
                            child: Text('${category.icon} ${category.name}'),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value;
                          _loadData();
                        });
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Trial list
        Expanded(
          child: _filteredTrials.isEmpty
              ? _buildEmptyHistoryState()
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                  itemCount: _filteredTrials.length,
                  itemBuilder: (context, index) {
                    final trial = _filteredTrials[index];
                    return TrialCard(
                      trial: trial,
                      onTap: () => _showTrialDetails(trial),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedTimeRange == value;
    return Padding(
      padding: const EdgeInsets.only(right: AppSpacing.sm),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedTimeRange = value;
            _loadData();
          });
        },
        selectedColor: AppTheme.primaryGreen.withOpacity(0.2),
        checkmarkColor: AppTheme.primaryGreen,
      ),
    );
  }

  Widget _buildEmptyHistoryState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'No History Found',
              style: AppTextStyles.heading3.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'No trials match your current filters. Try adjusting your search criteria.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showTrialDetails(Trial trial) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.8,
        minChildSize: 0.4,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppBorderRadius.xl),
            ),
          ),
          child: Column(
            children: [
              // Handle
              Container(
                margin: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(AppSpacing.lg),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Trial header
                      Row(
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(AppBorderRadius.md),
                              color: Colors.grey.shade100,
                            ),
                            child: trial.logoUrl != null
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                                    child: Image.network(
                                      trial.logoUrl!,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) =>
                                          const Icon(Icons.subscriptions),
                                    ),
                                  )
                                : const Icon(Icons.subscriptions),
                          ),
                          const SizedBox(width: AppSpacing.md),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  trial.serviceName,
                                  style: AppTextStyles.heading2,
                                ),
                                Text(
                                  trial.serviceCategory,
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: AppTheme.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: AppSpacing.lg),

                      // Trial details
                      _buildDetailRow('Start Date', DateFormat('MMM d, y').format(trial.startDate)),
                      _buildDetailRow('Trial Duration', '${trial.trialDurationDays} days'),
                      _buildDetailRow('Status', trial.status.name.toUpperCase()),
                      _buildDetailRow('Monthly Cost', '\$${trial.monthlyCost.toStringAsFixed(2)}'),

                      if (trial.status == TrialStatus.cancelled && trial.moneySaved != null)
                        _buildDetailRow('Money Saved', '\$${trial.moneySaved!.toStringAsFixed(2)}'),

                      if (trial.notes != null && trial.notes!.isNotEmpty) ...[
                        const SizedBox(height: AppSpacing.md),
                        Text(
                          'Notes',
                          style: AppTextStyles.heading3,
                        ),
                        const SizedBox(height: AppSpacing.sm),
                        Text(
                          trial.notes!,
                          style: AppTextStyles.bodyMedium,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
