import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late String _greeting;
  late String _userName;
  List<Trial> _activeTrials = [];
  List<Trial> _expiringTrials = [];
  TrialStats? _stats;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final user = AuthService.getCurrentUser();
    _userName = user?.name ?? 'User';
    _greeting = _getGreeting();
    _loadTrialData();
  }

  Future<void> _loadTrialData() async {
    setState(() => _isLoading = true);
    final activeTrials = await TrialService.getActiveTrials();
    final expiringTrials = await TrialService.getExpiringTrials(7);
    final stats = await TrialService.getTrialStats();
    setState(() {
      _activeTrials = activeTrials;
      _expiringTrials = expiringTrials;
      _stats = stats;
      _isLoading = false;
    });
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.getCurrentUser();
    final stats = _stats;

    return Scaffold(
      appBar: GreetingAppBar(
        userName: _userName,
        greeting: _greeting,
        notificationCount: _expiringTrials.length,
        onProfileTap: () {
          HapticFeedback.lightImpact();
          // Navigate to profile
        },
        onNotificationTap: () {
          HapticFeedback.lightImpact();
          // Show notifications
        },
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadTrialData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: AppSpacing.md),
                    if (user != null)
                      SavingsProgressCard(
                        currentSavings: stats?.monthlySavings ?? 0,
                        goalAmount: user.monthlySavingsGoal,
                        currency: user.currency,
                        onTap: () {
                          HapticFeedback.lightImpact();
                          // Navigate to savings details
                        },
                      ),
                    if (stats != null) ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                        child: Text(
                          'Quick Stats',
                          style: AppTextStyles.heading3.copyWith(
                            fontSize: 20 * MediaQuery.of(context).textScaleFactor,
                          ),
                        ),
                      ),
                      const SizedBox(height: AppSpacing.sm),
                      QuickStatsRow(
                        activeTrials: stats.activeTrials.toString(),
                        expiringThisWeek: stats.expiringThisWeek.toString(),
                        monthlySavings: stats.monthlySavings.toStringAsFixed(0),
                        totalSavings: stats.totalSavings.toStringAsFixed(0),
                        onActiveTrialsTap: () {
                          HapticFeedback.lightImpact();
                          // Navigate to active trials
                        },
                        onExpiringTap: () {
                          HapticFeedback.lightImpact();
                          // Navigate to expiring trials
                        },
                        onMonthlySavingsTap: () {
                          HapticFeedback.lightImpact();
                          // Navigate to monthly savings
                        },
                        onTotalSavingsTap: () {
                          HapticFeedback.lightImpact();
                          // Navigate to total savings
                        },
                      ),
                    ],
                    const SizedBox(height: AppSpacing.lg),
                    if (_expiringTrials.isNotEmpty) ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                        child: Row(
                          children: [
                            Icon(
                              Icons.warning,
                              color: AppTheme.warningOrange,
                              size: 20,
                              semanticLabel: 'Warning',
                            ),
                            const SizedBox(width: AppSpacing.sm),
                            Text(
                              'Expiring Soon',
                              style: AppTextStyles.heading3.copyWith(
                                color: MediaQuery.of(context).highContrast
                                    ? Colors.black
                                    : AppTheme.warningOrange,
                                fontSize: 20 * MediaQuery.of(context).textScaleFactor,
                              ),
                            ),
                            const Spacer(),
                            IconButton(
                              icon: const Icon(Icons.arrow_forward),
                              onPressed: () {
                                HapticFeedback.lightImpact();
                                // Navigate to all expiring trials
                              },
                              tooltip: 'View all expiring trials',
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppSpacing.sm),
                      SizedBox(
                        height: 200,
                        child: ListView.custom(
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                          childrenDelegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final trial = _expiringTrials[index];
                              return SizedBox(
                                width: 300,
                                child: Semantics(
                                  label: 'Trial card for ${trial.serviceName}, expires in ${trial.daysRemaining} days',
                                  child: TrialCard(
                                    trial: trial,
                                    onTap: () => _showTrialDetails(trial),
                                    onCancel: () => _cancelTrial(trial),
                                    onSnooze: () => _snoozeTrial(trial),
                                  ),
                                ),
                              );
                            },
                            childCount: _expiringTrials.length,
                          ),
                        ),
                      ),
                      const SizedBox(height: AppSpacing.lg),
                    ],
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                      child: Row(
                        children: [
                          Text(
                            'Active Trials',
                            style: AppTextStyles.heading3.copyWith(
                              fontSize: 20 * MediaQuery.of(context).textScaleFactor,
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            icon: const Icon(Icons.arrow_forward),
                            onPressed: () {
                              HapticFeedback.lightImpact();
                              // Navigate to all active trials
                            },
                            tooltip: 'View all active trials',
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    if (_activeTrials.isEmpty)
                      _buildEmptyState()
                    else
                      ListView.custom(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        childrenDelegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final trial = _activeTrials[index];
                            return Semantics(
                              label: 'Trial card for ${trial.serviceName}, expires in ${trial.daysRemaining} days',
                              child: TrialCard(
                                trial: trial,
                                onTap: () => _showTrialDetails(trial),
                                onCancel: () => _cancelTrial(trial),
                                onSnooze: () => _snoozeTrial(trial),
                              ),
                            );
                          },
                          childCount: _activeTrials.take(5).length,
                        ),
                      ),
                    const SizedBox(height: AppSpacing.xxl),
                  ],
                ),
              ),
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          HapticFeedback.lightImpact();
          // Navigate to add trial screen
        },
        icon: const Icon(Icons.add),
        label: const Text('Add Trial'),
        backgroundColor: Colors.transparent,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppBorderRadius.md)),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.sm),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.add),
              SizedBox(width: AppSpacing.xs),
              Text('Add Trial'),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.subscriptions), label: 'Trials'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
        currentIndex: 0,
        onTap: (index) {
          HapticFeedback.lightImpact();
          // Handle navigation
        },
        selectedItemColor: AppTheme.primaryGreen,
        unselectedItemColor: AppTheme.textSecondary,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          Icon(
            Icons.subscriptions_outlined,
            size: 64,
            color: Colors.grey.shade400,
            semanticLabel: 'No subscriptions',
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'No Active Trials',
            style: AppTextStyles.heading3.copyWith(
              color: Colors.grey.shade600,
              fontSize: 20 * MediaQuery.of(context).textScaleFactor,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Add your first trial to start tracking and saving money!',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton.icon(
            onPressed: () {
              HapticFeedback.lightImpact();
              // Navigate to add trial screen
            },
            icon: const Icon(Icons.add),
            label: const Text('Add Your First Trial'),
          ),
        ],
      ),
    );
  }

  void _showTrialDetails(Trial trial) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(AppBorderRadius.xl)),
          ),
          child: Stack(
            children: [
              Column(
                children: [
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      controller: scrollController,
                      padding: const EdgeInsets.all(AppSpacing.lg),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Hero(
                                tag: 'trial-logo-${trial.id}',
                                child: Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                                    color: Colors.grey.shade100,
                                  ),
                                  child: trial.logoUrl != null
                                      ? ClipRRect(
                                          borderRadius: BorderRadius.circular(AppBorderRadius.md),
                                          child: CachedNetworkImage(
                                            imageUrl: trial.logoUrl!,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) => const CircularProgressIndicator(),
                                            errorWidget: (context, url, error) => const Icon(Icons.subscriptions),
                                          ),
                                        )
                                      : const Icon(Icons.subscriptions),
                                ),
                              ),
                              const SizedBox(width: AppSpacing.md),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      trial.serviceName,
                                      style: AppTextStyles.heading2.copyWith(
                                        fontSize: 24 * MediaQuery.of(context).textScaleFactor,
                                      ),
                                    ),
                                    Text(
                                      trial.serviceCategory,
                                      style: AppTextStyles.bodyMedium.copyWith(
                                        color: AppTheme.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppSpacing.lg),
                          _buildDetailRow('Start Date', DateFormat('MMM d, y').format(trial.startDate)),
                          _buildDetailRow('Trial Duration', '${trial.trialDurationDays} days'),
                          _buildDetailRow('Expires', DateFormat('MMM d, y').format(trial.expiryDate)),
                          _buildDetailRow('Days Remaining', '${trial.daysRemaining} days'),
                          _buildDetailRow('Monthly Cost', '\$${trial.monthlyCost.toStringAsFixed(2)}'),
                          if (trial.notes != null && trial.notes!.isNotEmpty) ...[
                            const SizedBox(height: AppSpacing.md),
                            Text(
                              'Notes',
                              style: AppTextStyles.heading3.copyWith(
                                fontSize: 20 * MediaQuery.of(context).textScaleFactor,
                              ),
                            ),
                            const SizedBox(height: AppSpacing.sm),
                            Text(
                              trial.notes!,
                              style: AppTextStyles.bodyMedium,
                            ),
                          ],
                          const SizedBox(height: AppSpacing.xl),
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton(
                                  onPressed: () {
                                    HapticFeedback.lightImpact();
                                    Navigator.pop(context);
                                    _snoozeTrial(trial);
                                  },
                                  child: const Text('Snooze'),
                                ),
                              ),
                              const SizedBox(width: AppSpacing.md),
                              Expanded(
                                flex: 2,
                                child: ElevatedButton(
                                  onPressed: () {
                                    HapticFeedback.lightImpact();
                                    Navigator.pop(context);
                                    _cancelTrial(trial);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppTheme.urgentRed,
                                  ),
                                  child: const Text('Cancel Trial'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Positioned(
                top: AppSpacing.sm,
                right: AppSpacing.sm,
                child: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.pop(context);
                  },
                  tooltip: 'Close',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelTrial(Trial trial) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Trial'),
        content: Text(
          'Are you sure you want to cancel ${trial.serviceName}? This will save you \$${trial.monthlyCost.toStringAsFixed(2)}/month.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Keep Trial'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.urgentRed,
            ),
            child: const Text('Cancel Trial'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await TrialService.cancelTrial(trial.id);
      await _loadTrialData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${trial.serviceName} trial cancelled! You saved \$${trial.monthlyCost.toStringAsFixed(2)}/month.'),
            backgroundColor: AppTheme.successGreen,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _snoozeTrial(Trial trial) async {
    final snoozeDays = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Snooze Reminder'),
        content: const Text('How many days would you like to snooze this reminder?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 1),
            child: const Text('1 Day'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 3),
            child: const Text('3 Days'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 7),
            child: const Text('1 Week'),
          ),
        ],
      ),
    );

    if (snoozeDays != null) {
      await TrialService.snoozeTrialReminder(trial.id, snoozeDays);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${trial.serviceName} reminder snoozed for $snoozeDays days.'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}

class AppSpacing {
  static const xs = 4.0;
  static const sm = 8.0;
  static const md = 16.0;
  static const lg = 24.0;
  static const xl = 32.0;
  static const xxl = 48.0;
}

class AppBorderRadius {
  static const md = 12.0;
  static const xl = 24.0;
}

class AppTextStyles {
  static final heading2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: AppTheme.textPrimary,
    letterSpacing: 0.15,
  );
  static final heading3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppTheme.textPrimary,
    letterSpacing: 0.15,
  );
  static final bodyMedium = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: AppTheme.textPrimary,
    letterSpacing: 0.25,
  );
}

class AppTheme {
  static const primaryGreen = Color(0xFF4CAF50);
  static const warningOrange = Color(0xFFFFA500);
  static const urgentRed = Color(0xFFD32F2F);
  static const successGreen = Color(0xFF388E3C);
  static const textPrimary = Color(0xFF212121);
  static const textSecondary = Color(0xFF757575);
  static final primaryGradient = LinearGradient(
    colors: [primaryGreen, primaryGreen.withOpacity(0.8)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}

// Placeholder classes for compilation
class Trial {
  final String id;
  final String serviceName;
  final String serviceCategory;
  final String? logoUrl;
  final DateTime startDate;
  final DateTime expiryDate;
  final int trialDurationDays;
  final int daysRemaining;
  final double monthlyCost;
  final String? notes;

  Trial({
    required this.id,
    required this.serviceName,
    required this.serviceCategory,
    this.logoUrl,
    required this.startDate,
    required this.expiryDate,
    required this.trialDurationDays,
    required this.daysRemaining,
    required this.monthlyCost,
    this.notes,
  });
}

class TrialStats {
  final int activeTrials;
  final int expiringThisWeek;
  final double monthlySavings;
  final double totalSavings;

  TrialStats({
    required this.activeTrials,
    required this.expiringThisWeek,
    required this.monthlySavings,
    required this.totalSavings,
  });
}

class User {
  final String name;
  final double monthlySavingsGoal;
  final String currency;

  User({required this.name, required this.monthlySavingsGoal, required this.currency});
}

class AuthService {
  static User? getCurrentUser() => null;
}

class TrialService {
  static Future<List<Trial>> getActiveTrials() async => [];
  static Future<List<Trial>> getExpiringTrials(int days) async => [];
  static Future<TrialStats?> getTrialStats() async => null;
  static Future<void> cancelTrial(String id) async {}
  static Future<void> snoozeTrialReminder(String id, int days) async {}
}

class GreetingAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String userName;
  final String greeting;
  final int notificationCount;
  final VoidCallback onProfileTap;
  final VoidCallback onNotificationTap;

  const GreetingAppBar({
    super.key,
    required this.userName,
    required this.greeting,
    required this.notificationCount,
    required this.onProfileTap,
    required this.onNotificationTap,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text('$greeting, $userName'),
      actions: [
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications),
              onPressed: onNotificationTap,
            ),
            if (notificationCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: AppTheme.urgentRed,
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    '$notificationCount',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
          ],
        ),
        IconButton(
          icon: const Icon(Icons.person),
          onPressed: onProfileTap,
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class SavingsProgressCard extends StatelessWidget {
  final double currentSavings;
  final double goalAmount;
  final String currency;
  final VoidCallback onTap;

  const SavingsProgressCard({
    super.key,
    required this.currentSavings,
    required this.goalAmount,
    required this.currency,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Savings Progress',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppSpacing.sm),
            LinearProgressIndicator(
              value: goalAmount > 0 ? currentSavings / goalAmount : 0,
              backgroundColor: Colors.grey.shade200,
              valueColor: const AlwaysStoppedAnimation(AppTheme.primaryGreen),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              '$currency${currentSavings.toStringAsFixed(2)} / $currency${goalAmount.toStringAsFixed(2)}',
              style: AppTextStyles.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}

class QuickStatsRow extends StatelessWidget {
  final String activeTrials;
  final String expiringThisWeek;
  final String monthlySavings;
  final String totalSavings;
  final VoidCallback onActiveTrialsTap;
  final VoidCallback onExpiringTap;
  final VoidCallback onMonthlySavingsTap;
  final VoidCallback onTotalSavingsTap;

  const QuickStatsRow({
    super.key,
    required this.activeTrials,
    required this.expiringThisWeek,
    required this.monthlySavings,
    required this.totalSavings,
    required this.onActiveTrialsTap,
    required this.onExpiringTap,
    required this.onMonthlySavingsTap,
    required this.onTotalSavingsTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildStatCard('Active Trials', activeTrials, onActiveTrialsTap),
        _buildStatCard('Expiring This Week', expiringThisWeek, onExpiringTap),
        _buildStatCard('Monthly Savings', '\$$monthlySavings', onMonthlySavingsTap),
        _buildStatCard('Total Savings', '\$$totalSavings', onTotalSavingsTap),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            color: AppTheme.primaryGreen.withOpacity(0.1),
          ),
          child: Column(
            children: [
              Text(title, style: AppTextStyles.bodyMedium.copyWith(color: AppTheme.textSecondary)),
              const SizedBox(height: AppSpacing.xs),
              Text(value, style: AppTextStyles.heading3),
            ],
          ),
        ),
      ),
    );
  }
}

class TrialCard extends StatelessWidget {
  final Trial trial;
  final VoidCallback onTap;
  final VoidCallback onCancel;
  final VoidCallback onSnooze;

  const TrialCard({
    super.key,
    required this.trial,
    required this.onTap,
    required this.onCancel,
    required this.onSnooze,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.symmetric(horizontal: AppSpacing.sm, vertical: AppSpacing.xs),
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Hero(
                  tag: 'trial-logo-${trial.id}',
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppBorderRadius.md),
                      color: Colors.grey.shade100,
                    ),
                    child: trial.logoUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(AppBorderRadius.md),
                            child: CachedNetworkImage(
                              imageUrl: trial.logoUrl!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => const CircularProgressIndicator(),
                              errorWidget: (context, url, error) => const Icon(Icons.subscriptions),
                            ),
                          )
                        : const Icon(Icons.subscriptions),
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Text(
                    trial.serviceName,
                    style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Expires: ${DateFormat('MMM d, y').format(trial.expiryDate)}',
              style: AppTextStyles.bodyMedium.copyWith(color: AppTheme.textSecondary),
            ),
            const SizedBox(height: AppSpacing.xs),
            LinearProgressIndicator(
              value: trial.trialDurationDays > 0 ? trial.daysRemaining / trial.trialDurationDays : 0,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation(
                trial.daysRemaining <= 3 ? AppTheme.urgentRed : AppTheme.primaryGreen,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: onSnooze,
                    child: const Text('Snooze'),
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: ElevatedButton(
                    onPressed: onCancel,
                    style: ElevatedButton.styleFrom(backgroundColor: AppTheme.urgentRed),
                    child: const Text('Cancel'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}