import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/savings_progress_card.dart';
import '../../widgets/stats_card.dart';
import '../../widgets/trial_card.dart';
import '../../theme/app_theme.dart';
import '../../services/auth_service.dart';
import '../../services/trial_service.dart';
import '../../models/trial.dart';


// --- Mock Data and Utilities (for demonstration purposes) ---
// You should have your actual implementations for these
class User {
  final String name;
  final double monthlySavingsGoal;
  final String currency;
  User({required this.name, required this.monthlySavingsGoal, this.currency = '\$'});
}

class TrialStats {
  final int activeTrials;
  final int expiringThisWeek;
  final double monthlySavings;
  final double totalSavings;
  TrialStats({
    required this.activeTrials,
    required this.expiringThisWeek,
    required this.monthlySavings,
    required this.totalSavings,
  });
}

// Dummy Trial class - replace with your actual Trial model
class Trial {
  final String id;
  final String serviceName;
  final String serviceCategory;
  final String? logoUrl;
  final double monthlyCost;
  final DateTime startDate;
  final DateTime expiryDate;
  final int trialDurationDays;
  final String? notes;

  int get daysRemaining => expiryDate.difference(DateTime.now()).inDays;

  Trial({
    required this.id,
    required this.serviceName,
    required this.serviceCategory,
    this.logoUrl,
    required this.monthlyCost,
    required this.startDate,
    required this.expiryDate,
    required this.trialDurationDays,
    this.notes,
  });
}

// Dummy AuthService
class AuthService {
  static User? getCurrentUser() {
    return User(name: 'John Doe', monthlySavingsGoal: 500);
  }
}

// Dummy TrialService
class TrialService {
  static List<Trial> _allTrials = [
    Trial(
      id: '1',
      serviceName: 'Netflix Premium',
      serviceCategory: 'Streaming',
      logoUrl: 'https://cdn.iconscout.com/icon/free/png-256/free-netflix-3047244-2531301.png',
      monthlyCost: 15.99,
      startDate: DateTime.now().subtract(const Duration(days: 10)),
      expiryDate: DateTime.now().add(const Duration(days: 5)),
      trialDurationDays: 30,
      notes: 'Remember to cancel before billing starts.',
    ),
    Trial(
      id: '2',
      serviceName: 'Spotify Family',
      serviceCategory: 'Music',
      logoUrl: 'https://www.scdn.co/i/_global/favicon.png',
      monthlyCost: 9.99,
      startDate: DateTime.now().subtract(const Duration(days: 20)),
      expiryDate: DateTime.now().add(const Duration(days: 2)),
      trialDurationDays: 30,
    ),
    Trial(
      id: '3',
      serviceName: 'Amazon Prime',
      serviceCategory: 'Shopping',
      logoUrl: 'https://m.media-amazon.com/images/G/01/social/api-share/amazon_logo_500x500.png',
      monthlyCost: 12.99,
      startDate: DateTime.now().subtract(const Duration(days: 5)),
      expiryDate: DateTime.now().add(const Duration(days: 25)),
      trialDurationDays: 30,
    ),
    Trial(
      id: '4',
      serviceName: 'Adobe Creative Cloud',
      serviceCategory: 'Software',
      logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e0/Adobe_Corporate_Logo.png/600px-Adobe_Corporate_Logo.png',
      monthlyCost: 52.99,
      startDate: DateTime.now().subtract(const Duration(days: 1)),
      expiryDate: DateTime.now().add(const Duration(days: 6)),
      trialDurationDays: 7,
    ),
  ];

  static List<Trial> getActiveTrials() {
    // Filter out trials that have expired and are not cancelled
    return _allTrials.where((trial) => trial.expiryDate.isAfter(DateTime.now())).toList();
  }

  static List<Trial> getExpiringTrials(int days) {
    return getActiveTrials().where((trial) => trial.daysRemaining <= days).toList();
  }

  static TrialStats getTrialStats() {
    final active = getActiveTrials().length;
    final expiring = getExpiringTrials(7).length;
    final monthlySaved = 120.0; // Dummy value
    final totalSaved = 850.0; // Dummy value
    return TrialStats(
      activeTrials: active,
      expiringThisWeek: expiring,
      monthlySavings: monthlySaved,
      totalSavings: totalSaved,
    );
  }

  static Future<void> cancelTrial(String id) async {
    await Future.delayed(const Duration(milliseconds: 500));
    _allTrials.removeWhere((trial) => trial.id == id);
  }

  static Future<void> snoozeTrialReminder(String id, int days) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, you'd update the trial's reminder date
  }
}

// Dummy AppTheme (replace with your actual AppTheme)
class AppTheme {
  static Color warningOrange = Colors.orange.shade700;
  static Color urgentRed = Colors.red.shade700;
  static Color successGreen = Colors.green.shade600;

  static ThemeData lightTheme = ThemeData(
    primaryColor: Colors.deepPurple,
    colorScheme: ColorScheme.light(
      primary: Colors.deepPurple,
      secondary: Colors.purpleAccent,
      surface: Colors.white,
      background: Colors.grey.shade50,
      onSurface: Colors.grey.shade900,
      onPrimary: Colors.white,
      outline: Colors.grey.shade300,
      shadow: Colors.black,
    ),
    textTheme: TextTheme(
      headlineLarge: TextStyle(fontSize: 32, color: Colors.grey.shade900),
      headlineSmall: TextStyle(fontSize: 20, color: Colors.grey.shade900),
      titleMedium: TextStyle(fontSize: 18, color: Colors.grey.shade900),
      bodyLarge: TextStyle(fontSize: 16, color: Colors.grey.shade800),
      bodyMedium: TextStyle(fontSize: 14, color: Colors.grey.shade700),
      bodySmall: TextStyle(fontSize: 12, color: Colors.grey.shade600),
      labelLarge: TextStyle(fontSize: 16, color: Colors.white),
      labelSmall: TextStyle(fontSize: 10, color: Colors.grey.shade500),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.deepPurple,
        side: BorderSide(color: Colors.deepPurple.withOpacity(0.5)),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: Colors.deepPurple,
      ),
    ),
  );

  static Color textSecondary = Colors.grey.shade600;
}

extension ColorAlphaExtension on Color {
  Color withValues({double? red, double? green, double? blue, double? alpha}) {
    return Color.fromRGBO(
      red != null ? red.toInt() : this.red,
      green != null ? green.toInt() : this.green,
      blue != null ? blue.toInt() : this.blue,
      (alpha ?? this.opacity) * 255 ~/ 1, // Convert 0-1 alpha to 0-255
    );
  }
}

// Dummy AppSpacing (replace with your actual AppSpacing)
class AppSpacing {
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
  static const double xxl = 64.0;
}

// Dummy AppBorderRadius (replace with your actual AppBorderRadius)
class AppBorderRadius {
  static const double sm = 8.0;
  static const double md = 12.0;
  static const double lg = 16.0;
  static const double xl = 24.0;
  static const double circular = 100.0;
}

// Dummy AppTextStyles (replace with your actual AppTextStyles)
class AppTextStyles {
  static TextStyle heading1 = const TextStyle(fontSize: 28, fontWeight: FontWeight.bold);
  static TextStyle heading2 = const TextStyle(fontSize: 24, fontWeight: FontWeight.bold);
  static TextStyle heading3 = const TextStyle(fontSize: 20, fontWeight: FontWeight.bold);

  static TextStyle heading3Primary(BuildContext context) => Theme.of(context).textTheme.headlineSmall!.copyWith(
    fontWeight: FontWeight.bold,
    color: Theme.of(context).colorScheme.onSurface,
  );

  static TextStyle bodyMedium = const TextStyle(fontSize: 14);
  static TextStyle bodyMediumSecondary(BuildContext context) => Theme.of(context).textTheme.bodyMedium!.copyWith(
    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
  );
  static TextStyle caption = const TextStyle(fontSize: 12);
}

// Dummy AppNavigation (replace with your actual AppNavigation)
class AppNavigation {
  static void goToAddTrial(BuildContext context) {
    // Implement your navigation logic, e.g., context.go('/add-trial');
    print('Navigating to Add Trial screen');
    // For demonstration, just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Navigate to Add Trial Screen')),
    );
  }
}

// Dummy GreetingAppBar (replace with your actual GreetingAppBar or integrate directly)
class GreetingAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String userName;
  final String greeting;
  final int notificationCount;
  final VoidCallback onProfileTap;
  final VoidCallback onNotificationTap;

  const GreetingAppBar({
    Key? key,
    required this.userName,
    required this.greeting,
    this.notificationCount = 0,
    required this.onProfileTap,
    required this.onNotificationTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      systemOverlayStyle: SystemUiOverlayStyle.dark,
      toolbarHeight: 80,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0.1),
              Theme.of(context).colorScheme.secondary.withOpacity(0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: Row(
              children: [
                // Greeting Section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        greeting,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Text(
                        userName,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),

                // Notification Badge
                Stack(
                  children: [
                    InkWell( // Use InkWell for better tap feedback
                      onTap: onNotificationTap,
                      borderRadius: BorderRadius.circular(AppBorderRadius.md),
                      child: Container(
                        padding: const EdgeInsets.all(AppSpacing.sm),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(AppBorderRadius.md),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.notifications_outlined,
                          color: Theme.of(context).colorScheme.onSurface,
                          size: 24,
                        ),
                      ),
                    ),
                    if (notificationCount > 0)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(AppSpacing.xs),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.surface,
                              width: 2,
                            ),
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 20,
                            minHeight: 20,
                          ),
                          child: Text(
                            '$notificationCount',
                            style: AppTextStyles.caption.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(width: AppSpacing.md),

                // Profile Avatar
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    onProfileTap();
                  },
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppBorderRadius.md),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Theme.of(context).colorScheme.primary,
                          Theme.of(context).colorScheme.secondary,
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                        style: AppTextStyles.heading3.copyWith(color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(80);
}

// Dummy QuickStatsRow (replace with your actual QuickStatsRow)
class QuickStatsRow extends StatelessWidget {
  final String activeTrials;
  final String expiringThisWeek;
  final String monthlySavings;
  final String totalSavings;
  final VoidCallback? onActiveTrialsTap;
  final VoidCallback? onExpiringTap;
  final VoidCallback? onMonthlySavingsTap;
  final VoidCallback? onTotalSavingsTap;

  const QuickStatsRow({
    Key? key,
    required this.activeTrials,
    required this.expiringThisWeek,
    required this.monthlySavings,
    required this.totalSavings,
    this.onActiveTrialsTap,
    this.onExpiringTap,
    this.onMonthlySavingsTap,
    this.onTotalSavingsTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.subscriptions_outlined,
                title: 'Active Trials',
                value: activeTrials,
                color: Theme.of(context).colorScheme.primary,
                onTap: onActiveTrialsTap,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.schedule_outlined,
                title: 'Expiring Soon',
                value: expiringThisWeek,
                color: AppTheme.warningOrange,
                onTap: onExpiringTap,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.trending_up_outlined,
                title: 'Total Saved',
                value: '\$$totalSavings',
                color: AppTheme.successGreen,
                onTap: onTotalSavingsTap,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.calendar_today_outlined,
                title: 'This Month',
                value: '\$$monthlySavings',
                color: Colors.blue,
                onTap: onMonthlySavingsTap,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
      BuildContext context, {
        required IconData icon,
        required String title,
        required String value,
        required Color color,
        VoidCallback? onTap,
      }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppBorderRadius.xl),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(AppBorderRadius.xl),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppBorderRadius.md),
              ),
              child: Icon(
                icon,
                color: color,
                size: 20,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              title,
              style: AppTextStyles.bodyMediumSecondary(context),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Dummy SavingsProgressCard (replace with your actual SavingsProgressCard)
class SavingsProgressCard extends StatelessWidget {
  final double currentSavings;
  final double goalAmount;
  final String currency;
  final VoidCallback? onTap;

  const SavingsProgressCard({
    Key? key,
    required this.currentSavings,
    required this.goalAmount,
    this.currency = '\$',
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppBorderRadius.xl),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppBorderRadius.xl),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primary.withOpacity(0.8),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                  ),
                  child: const Icon(
                    Icons.savings_outlined,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                  ),
                  child: Text(
                    'This Month',
                    style: AppTextStyles.bodySmall?.copyWith(
                      color: Colors.white.withOpacity(0.9),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'Monthly Savings',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white.withOpacity(0.9),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '$currency${currentSavings.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 36,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Padding(
                  padding: const EdgeInsets.only(bottom: AppSpacing.xs),
                  child: Text(
                    '/ $currency${goalAmount.toStringAsFixed(0)}',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withOpacity(0.7),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            ClipRRect(
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              child: LinearProgressIndicator(
                value: goalAmount > 0 ? currentSavings / goalAmount : 0,
                backgroundColor: Colors.white.withOpacity(0.2),
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                minHeight: 8,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Dummy TrialCard (replace with your actual TrialCard)
class TrialCard extends StatelessWidget {
  final Trial trial;
  final VoidCallback onTap;
  final VoidCallback onCancel;
  final VoidCallback onSnooze;
  final bool isExpiring; // Added this to differentiate UI

  const TrialCard({
    Key? key,
    required this.trial,
    required this.onTap,
    required this.onCancel,
    required this.onSnooze,
    this.isExpiring = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        border: isExpiring
            ? Border.all(color: AppTheme.warningOrange.withOpacity(0.3), width: 2)
            : Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: isExpiring
                ? AppTheme.warningOrange.withOpacity(0.1)
                : Theme.of(context).colorScheme.shadow.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppBorderRadius.xl),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(AppBorderRadius.md),
                        color: Colors.grey.shade100,
                      ),
                      child: trial.logoUrl != null && Uri.parse(trial.logoUrl!).isAbsolute
                          ? ClipRRect(
                        borderRadius: BorderRadius.circular(AppBorderRadius.md),
                        child: Image.network(
                          trial.logoUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Center(
                            child: Icon(
                              Icons.subscriptions,
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                            ),
                          ),
                        ),
                      )
                          : Center(
                        child: Icon(
                          Icons.subscriptions,
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppSpacing.md),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            trial.serviceName,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppSpacing.xs),
                          Text(
                            trial.serviceCategory,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.sm),
                      decoration: BoxDecoration(
                        color: isExpiring
                            ? AppTheme.warningOrange.withOpacity(0.1)
                            : Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                      ),
                      child: Text(
                        '\$${trial.monthlyCost.toStringAsFixed(0)}/mo',
                        style: AppTextStyles.caption.copyWith(
                          color: isExpiring
                              ? AppTheme.warningOrange
                              : Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppSpacing.md),

                // Progress bar
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Days remaining',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                              Text(
                                '${trial.daysRemaining} days',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: isExpiring ? AppTheme.warningOrange : null,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppSpacing.xs),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(AppBorderRadius.xs),
                            child: LinearProgressIndicator(
                              value: trial.trialDurationDays > 0 ? trial.daysRemaining / trial.trialDurationDays : 0,
                              backgroundColor: Colors.grey.shade200,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                isExpiring ? AppTheme.warningOrange : Theme.of(context).colorScheme.primary,
                              ),
                              minHeight: 6,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppSpacing.md),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: onSnooze,
                        icon: const Icon(Icons.snooze, size: 16),
                        label: const Text('Snooze'),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppBorderRadius.md),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppSpacing.md),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: onCancel,
                        icon: const Icon(Icons.cancel_outlined, size: 16),
                        label: const Text('Cancel'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.urgentRed,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppBorderRadius.md),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Dummy AppResponsive (You would place this in a separate file like `utils/app_responsive.dart`)
class AppResponsive {
  static bool isMobile(BuildContext context) => MediaQuery.of(context).size.width < 600;
  static bool isTablet(BuildContext context) => MediaQuery.of(context).size.width >= 600 && MediaQuery.of(context).size.width < 1200;
  static bool isDesktop(BuildContext context) => MediaQuery.of(context).size.width >= 1200;

  static double horizontalPadding(BuildContext context) {
    if (isDesktop(context)) {
      return AppSpacing.xxl;
    } else if (isTablet(context)) {
      return AppSpacing.xl;
    }
    return AppSpacing.md;
  }
}

// Shimmer effect placeholder
class ShimmerLoading extends StatelessWidget {
  const ShimmerLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AppBar space
          const SizedBox(height: 80), // Match app bar height

          // Shimmer for SavingsProgressCard
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppResponsive.horizontalPadding(context)),
            child: Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(AppBorderRadius.xl),
              ),
            ),
          ),
          const SizedBox(height: AppSpacing.lg),

          // Shimmer for Quick Stats title
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppResponsive.horizontalPadding(context)),
            child: Container(
              width: 150,
              height: 24,
              color: Colors.grey.shade300,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          // Shimmer for Quick Stats cards
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppResponsive.horizontalPadding(context)),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(AppBorderRadius.xl),
                    ),
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Container(
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(AppBorderRadius.xl),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppResponsive.horizontalPadding(context)),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(AppBorderRadius.xl),
                    ),
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Container(
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(AppBorderRadius.xl),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppSpacing.xl),

          // Shimmer for Expiring Trials title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: Container(
              width: 200,
              height: 24,
              color: Colors.grey.shade300,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          // Shimmer for Expiring Trials cards (horizontal scroll)
          SizedBox(
            height: 220,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
              itemCount: 2, // Show a couple of shimmer cards
              itemBuilder: (context, index) {
                return Container(
                  width: 320,
                  margin: const EdgeInsets.only(right: AppSpacing.md),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(AppBorderRadius.xl),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: AppSpacing.xl),

          // Shimmer for Active Trials title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: Container(
              width: 200,
              height: 24,
              color: Colors.grey.shade300,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          // Shimmer for Active Trials list
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: Column(
              children: List.generate(3, (index) => Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.md),
                child: Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(AppBorderRadius.xl),
                  ),
                ),
              )),
            ),
          ),
        ],
      ),
    );
  }
}


class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late String _greeting;
  late String _userName;
  List<Trial> _activeTrials = [];
  List<Trial> _expiringTrials = [];
  TrialStats? _stats;
  bool _isLoading = true; // Added loading state

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() async {
    final user = AuthService.getCurrentUser();
    _userName = user?.name ?? 'User';
    _greeting = _getGreeting();
    await _loadTrialData();
  }

  Future<void> _loadTrialData() async {
    setState(() => _isLoading = true);
    // Simulate loading delay for smooth animation
    await Future.delayed(const Duration(milliseconds: 700)); // Increased delay for shimmer visibility
    setState(() {
      _activeTrials = TrialService.getActiveTrials();
      _expiringTrials = TrialService.getExpiringTrials(7);
      _stats = TrialService.getTrialStats();
      _isLoading = false;
    });
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 17) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.getCurrentUser();
    final stats = _stats;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: GreetingAppBar(
        userName: _userName,
        greeting: _greeting,
        notificationCount: _expiringTrials.length,
        onProfileTap: () {
          HapticFeedback.lightImpact(); // Added haptic feedback
          // Navigate to profile
        },
        onNotificationTap: () {
          HapticFeedback.lightImpact(); // Added haptic feedback
          // Show notifications (maybe navigate to a notification screen)
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Show Notifications')),
          );
        },
      ),
      body: _isLoading ? const ShimmerLoading() : _buildContent(user, stats),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          HapticFeedback.lightImpact(); // Added haptic feedback
          AppNavigation.goToAddTrial(context);
        },
        icon: const Icon(Icons.add),
        label: const Text('Add Trial'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        elevation: 4,
        extendedPadding: const EdgeInsets.symmetric(horizontal: 20),
      ),
    );
  }

  Widget _buildContent(user, stats) {
    return RefreshIndicator(
      onRefresh: _loadTrialData,
      color: Theme.of(context).colorScheme.primary,
      child: SingleChildScrollView( // Changed CustomScrollView to SingleChildScrollView for simplicity with Column
        physics: const AlwaysScrollableScrollPhysics(), // Ensures pull-to-refresh works
        padding: const EdgeInsets.only(bottom: AppSpacing.xxl), // Add bottom padding to prevent FAB overlap
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppSpacing.lg), // Increased initial spacing

            // Savings Progress Card
            if (user != null)
              Padding(
                padding: EdgeInsets.symmetric(horizontal: AppResponsive.horizontalPadding(context)),
                child: SavingsProgressCard(
                  currentSavings: stats?.monthlySavings ?? 0,
                  goalAmount: user.monthlySavingsGoal,
                  currency: user.currency,
                  onTap: () {
                    HapticFeedback.lightImpact();
                    // Navigate to savings details
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Navigate to Savings Details')),
                    );
                  },
                ),
              ),

            const SizedBox(height: AppSpacing.xl),

            // Quick Stats
            if (stats != null) ...[
              Padding(
                padding: EdgeInsets.symmetric(horizontal: AppResponsive.horizontalPadding(context)),
                child: Text(
                  'Overview', // Changed to Overview as per original design
                  style: AppTextStyles.heading3Primary(context),
                ),
              ),
              const SizedBox(height: AppSpacing.md),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: AppResponsive.horizontalPadding(context)),
                child: QuickStatsRow(
                  activeTrials: stats.activeTrials.toString(),
                  expiringThisWeek: stats.expiringThisWeek.toString(),
                  monthlySavings: stats.monthlySavings.toStringAsFixed(0),
                  totalSavings: stats.totalSavings.toStringAsFixed(0),
                  onActiveTrialsTap: () {
                    HapticFeedback.lightImpact();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Navigate to Active Trials')),
                    );
                  },
                  onExpiringTap: () {
                    HapticFeedback.lightImpact();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Navigate to Expiring Trials')),
                    );
                  },
                  onMonthlySavingsTap: () {
                    HapticFeedback.lightImpact();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Navigate to Monthly Savings')),
                    );
                  },
                  onTotalSavingsTap: () {
                    HapticFeedback.lightImpact();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Navigate to Total Savings')),
                    );
                  },
                ),
              ),
            ],

            const SizedBox(height: AppSpacing.xl),

            // Expiring Trials Section
            if (_expiringTrials.isNotEmpty) ...[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppSpacing.sm),
                      decoration: BoxDecoration(
                        color: AppTheme.warningOrange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppBorderRadius.md),
                      ),
                      child: Icon(
                        Icons.warning_amber_outlined, // Changed to outline for consistency with other icons
                        color: AppTheme.warningOrange,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.md),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Expiring Soon',
                            style: AppTextStyles.heading3Primary(context),
                          ),
                          Text(
                            'Don\'t forget to cancel these trials',
                            style: AppTextStyles.bodyMediumSecondary(context),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.md,
                        vertical: AppSpacing.sm,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.warningOrange,
                        borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.warningOrange.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        '${_expiringTrials.length}',
                        style: AppTextStyles.caption.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppSpacing.md),
              SizedBox(
                height: 220, // Adjusted height for TrialCard's new padding/content
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                  itemCount: _expiringTrials.length,
                  itemBuilder: (context, index) {
                    final trial = _expiringTrials[index];
                    return Container(
                      width: 300, // Slightly reduced width for better fit on smaller screens
                      margin: const EdgeInsets.only(right: AppSpacing.md),
                      child: TrialCard(
                        trial: trial,
                        isExpiring: true, // Pass this to TrialCard
                        onTap: () {
                          HapticFeedback.lightImpact();
                          _showTrialDetails(trial);
                        },
                        onCancel: () {
                          HapticFeedback.lightImpact();
                          _cancelTrial(trial);
                        },
                        onSnooze: () {
                          HapticFeedback.lightImpact();
                          _snoozeTrial(trial);
                        },
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: AppSpacing.xl),
            ],

            // Active Trials Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.sm),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppBorderRadius.md),
                    ),
                    child: Icon(
                      Icons.subscriptions_outlined, // Changed to outline for consistency
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Active Trials',
                          style: AppTextStyles.heading3Primary(context),
                        ),
                        Text(
                          'Manage your subscriptions',
                          style: AppTextStyles.bodyMediumSecondary(context),
                        ),
                      ],
                    ),
                  ),
                  if (_activeTrials.isNotEmpty)
                    TextButton.icon(
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        // Navigate to all active trials
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Navigate to All Active Trials')),
                        );
                      },
                      icon: const Icon(Icons.arrow_forward_ios, size: 16),
                      label: const Text('View All'),
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(height: AppSpacing.md),

            // Active Trials List
            if (_activeTrials.isEmpty)
              _buildEmptyState()
            else
              Container(
                margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _activeTrials.take(5).length, // Limiting to 5 as per original logic
                  itemBuilder: (context, index) {
                    final trial = _activeTrials[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: AppSpacing.md),
                      child: TrialCard(
                        trial: trial,
                        onTap: () {
                          HapticFeedback.lightImpact();
                          _showTrialDetails(trial);
                        },
                        onCancel: () {
                          HapticFeedback.lightImpact();
                          _cancelTrial(trial);
                        },
                        onSnooze: () {
                          HapticFeedback.lightImpact();
                          _snoozeTrial(trial);
                        },
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.lg),
      padding: const EdgeInsets.all(AppSpacing.xl),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2), // Adjusted opacity
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppSpacing.lg),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppBorderRadius.circular),
            ),
            child: Icon(
              Icons.subscriptions_rounded,
              size: 48,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            'No Active Trials Yet!', // More engaging text
            style: AppTextStyles.heading3Primary(context).copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'It looks like you don\'t have any trials being tracked. Add your first trial to start saving money and stay organized!',
            style: AppTextStyles.bodyMediumSecondary(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.xl),
          ElevatedButton.icon(
            onPressed: () {
              HapticFeedback.lightImpact(); // Added haptic feedback
              AppNavigation.goToAddTrial(context);
            },
            icon: const Icon(Icons.add_rounded),
            label: const Text('Add Your First Trial'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              elevation: 2,
              shadowColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            ),
          ),
        ],
      ),
    );
  }

  void _showTrialDetails(Trial trial) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppBorderRadius.xl),
            ),
          ),
          child: Column(
            children: [
              // Handle
              Container(
                margin: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(AppSpacing.lg),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Trial header
                      Row(
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(AppBorderRadius.md),
                              color: Colors.grey.shade100,
                            ),
                            child: trial.logoUrl != null && Uri.parse(trial.logoUrl!).isAbsolute
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                                    child: Image.network(
                                      trial.logoUrl!,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) => Center(
                                        child: Icon(
                                          Icons.subscriptions,
                                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                                          size: 30, // Larger icon for error
                                        ),
                                      ),
                                    ),
                                  )
                                : Center(
                                    child: Icon(
                                      Icons.subscriptions,
                                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                                      size: 30, // Larger icon for error
                                    ),
                                  ),
                          ),
                          const SizedBox(width: AppSpacing.md),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  trial.serviceName,
                                  style: AppTextStyles.heading2.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                                Text(
                                  trial.serviceCategory,
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: AppSpacing.lg),

                      // Trial details
                      _buildDetailRow(
                        context,
                        'Start Date',
                        DateFormat('MMM d, y').format(trial.startDate),
                      ),
                      _buildDetailRow(
                        context,
                        'Trial Duration',
                        '${trial.trialDurationDays} days',
                      ),
                      _buildDetailRow(
                        context,
                        'Expires',
                        DateFormat('MMM d, y').format(trial.expiryDate),
                        isHighlight: trial.daysRemaining <= 7, // Highlight if expiring soon
                      ),
                      _buildDetailRow(
                        context,
                        'Days Remaining',
                        '${trial.daysRemaining} days',
                        valueColor: trial.daysRemaining <= 7 ? AppTheme.warningOrange : null,
                      ),
                      _buildDetailRow(
                        context,
                        'Monthly Cost',
                        '\$${trial.monthlyCost.toStringAsFixed(2)}',
                      ),

                      if (trial.notes != null && trial.notes!.isNotEmpty) ...[
                        const SizedBox(height: AppSpacing.md),
                        Text(
                          'Notes',
                          style: AppTextStyles.heading3.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.sm),
                        Text(
                          trial.notes!,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],

                      const SizedBox(height: AppSpacing.xl),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                HapticFeedback.lightImpact();
                                Navigator.pop(context);
                                _snoozeTrial(trial);
                              },
                              child: const Text('Snooze'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Theme.of(context).colorScheme.primary,
                                side: BorderSide(color: Theme.of(context).colorScheme.primary.withOpacity(0.5)),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                                ),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: AppSpacing.md),
                          Expanded(
                            flex: 2,
                            child: ElevatedButton(
                              onPressed: () {
                                HapticFeedback.lightImpact();
                                Navigator.pop(context);
                                _cancelTrial(trial);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.urgentRed,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                                ),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: const Text('Cancel Trial'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
      BuildContext context,
      String label,
      String value, {
        bool isHighlight = false,
        Color? valueColor,
      }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: valueColor ?? (isHighlight ? AppTheme.warningOrange : Theme.of(context).colorScheme.onSurface),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelTrial(Trial trial) async {
    HapticFeedback.lightImpact(); // Added haptic feedback
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Trial'),
        content: Text(
          'Are you sure you want to cancel ${trial.serviceName}? This will save you \$${trial.monthlyCost.toStringAsFixed(2)}/month.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context, false);
            },
            child: const Text('Keep Trial'),
          ),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context, true);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.urgentRed,
            ),
            child: const Text('Cancel Trial'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await TrialService.cancelTrial(trial.id);
      _loadTrialData(); // Reload data after cancellation

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${trial.serviceName} trial cancelled! You saved \$${trial.monthlyCost.toStringAsFixed(2)}/month.'),
            backgroundColor: AppTheme.successGreen,
            behavior: SnackBarBehavior.floating, // Modern snackbar look
            margin: const EdgeInsets.all(AppSpacing.md),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppBorderRadius.md)),
          ),
        );
      }
    }
  }

  Future<void> _snoozeTrial(Trial trial) async {
    HapticFeedback.lightImpact(); // Added haptic feedback
    final snoozeDays = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Snooze Reminder'),
        content: const Text('How many days would you like to snooze this reminder?'),
        actions: [
          TextButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context, 1);
            },
            child: const Text('1 Day'),
          ),
          TextButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context, 3);
            },
            child: const Text('3 Days'),
          ),
          TextButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context, 7);
            },
            child: const Text('1 Week'),
          ),
        ],
      ),
    );

    if (snoozeDays != null) {
      await TrialService.snoozeTrialReminder(trial.id, snoozeDays);
      _loadTrialData(); // Reload data after snooze

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${trial.serviceName} reminder snoozed for $snoozeDays days.'),
            backgroundColor: Theme.of(context).colorScheme.primary, // Use primary color for snooze
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(AppSpacing.md),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppBorderRadius.md)),
          ),
        );
      }
    }
  }
}