import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../screens/auth/sign_in_screen.dart';
import '../screens/auth/sign_up_screen.dart';
import '../screens/auth/forgot_password_screen.dart';
import '../screens/main/main_screen.dart';
import '../screens/onboarding/onboarding_screen.dart';
import '../screens/onboarding/welcome_screen.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    redirect: _redirect,
    routes: [
      // Root route - determines initial screen
      GoRoute(
        path: '/',
        builder: (context, state) => const WelcomeScreen(),
      ),
      
      // Onboarding routes
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      
      // Authentication routes
      GoRoute(
        path: '/sign-in',
        builder: (context, state) => const SignInScreen(),
      ),
      GoRoute(
        path: '/sign-up',
        builder: (context, state) => const SignUpScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      
      // Main app routes
      ShellRoute(
        builder: (context, state, child) => MainScreen(child: child),
        routes: [
          GoRoute(
            path: '/home',
            builder: (context, state) => const SizedBox(), // Home content is in MainScreen
          ),
          GoRoute(
            path: '/add-trial',
            builder: (context, state) => const SizedBox(), // Add trial content is in MainScreen
          ),
          GoRoute(
            path: '/history',
            builder: (context, state) => const SizedBox(), // History content is in MainScreen
          ),
          GoRoute(
            path: '/profile',
            builder: (context, state) => const SizedBox(), // Profile content is in MainScreen
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.error?.toString() ?? 'Unknown error',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );

  static String? _redirect(BuildContext context, GoRouterState state) {
    final isSignedIn = AuthService.isSignedIn();
    final isFirstLaunch = StorageService.isFirstLaunch();
    final isOnboardingCompleted = StorageService.isOnboardingCompleted();
    final currentPath = state.uri.path;

    // If it's the first launch, show welcome screen
    if (isFirstLaunch && currentPath == '/') {
      return null; // Stay on welcome screen
    }

    // If onboarding is not completed and user is not on onboarding screen
    if (!isOnboardingCompleted && !currentPath.startsWith('/onboarding') && currentPath != '/') {
      return '/onboarding';
    }

    // If user is not signed in and trying to access protected routes
    if (!isSignedIn && _isProtectedRoute(currentPath)) {
      return '/sign-in';
    }

    // If user is signed in and trying to access auth routes
    if (isSignedIn && _isAuthRoute(currentPath)) {
      return '/home';
    }

    // If user is signed in but on root path, redirect to home
    if (isSignedIn && currentPath == '/') {
      return '/home';
    }

    return null; // No redirect needed
  }

  static bool _isProtectedRoute(String path) {
    const protectedRoutes = ['/home', '/add-trial', '/history', '/profile'];
    return protectedRoutes.any((route) => path.startsWith(route));
  }

  static bool _isAuthRoute(String path) {
    const authRoutes = ['/sign-in', '/sign-up', '/forgot-password'];
    return authRoutes.contains(path);
  }
}

// Navigation helper class
class AppNavigation {
  static void goToHome(BuildContext context) {
    context.go('/home');
  }

  static void goToAddTrial(BuildContext context) {
    context.go('/add-trial');
  }

  static void goToHistory(BuildContext context) {
    context.go('/history');
  }

  static void goToProfile(BuildContext context) {
    context.go('/profile');
  }

  static void goToSignIn(BuildContext context) {
    context.go('/sign-in');
  }

  static void goToSignUp(BuildContext context) {
    context.go('/sign-up');
  }

  static void goToForgotPassword(BuildContext context) {
    context.go('/forgot-password');
  }

  static void goToOnboarding(BuildContext context) {
    context.go('/onboarding');
  }

  static void signOut(BuildContext context) async {
    await AuthService.signOut();
    if (context.mounted) {
      context.go('/sign-in');
    }
  }
}
