import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class StatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? color;
  final String? subtitle;
  final VoidCallback? onTap;

  const StatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.color,
    this.subtitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final cardColor = color ?? Theme.of(context).colorScheme.primary;

    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppBorderRadius.xl),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                cardColor.withValues(alpha: 0.08),
                cardColor.withValues(alpha: 0.12),
              ],
            ),
            border: Border.all(
              color: cardColor.withValues(alpha: 0.2),
            ),
            boxShadow: [
              BoxShadow(
                color: cardColor.withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.sm),
                    decoration: BoxDecoration(
                      color: cardColor.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                    ),
                    child: Icon(
                      icon,
                      color: cardColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      title,
                      style: AppTextStyles.bodySmallSecondary(context).copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (onTap != null)
                    Container(
                      padding: const EdgeInsets.all(AppSpacing.xs),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                      ),
                      child: Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: 12,
                        color: AppTheme.getTextSecondary(context),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: AppSpacing.md),

              // Value
              Flexible(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm,
                    vertical: AppSpacing.xs,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                    border: Border.all(
                      color: cardColor.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Text(
                    value,
                    style: AppTextStyles.heading3.copyWith(
                      color: cardColor,
                      fontWeight: FontWeight.w800,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

              // Subtitle
              if (subtitle != null) ...[
                const SizedBox(height: AppSpacing.sm),
                Text(
                  subtitle!,
                  style: AppTextStyles.bodySmallSecondary(context).copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class StatsGrid extends StatelessWidget {
  final List<StatsCard> cards;
  final int crossAxisCount;

  const StatsGrid({
    super.key,
    required this.cards,
    this.crossAxisCount = 2,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: 1.4,
        crossAxisSpacing: AppSpacing.sm,
        mainAxisSpacing: AppSpacing.sm,
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) => cards[index],
    );
  }
}

class QuickStatsRow extends StatelessWidget {
  final String activeTrials;
  final String expiringThisWeek;
  final String monthlySavings;
  final String totalSavings;
  final VoidCallback? onActiveTrialsTap;
  final VoidCallback? onExpiringTap;
  final VoidCallback? onMonthlySavingsTap;
  final VoidCallback? onTotalSavingsTap;

  const QuickStatsRow({
    super.key,
    required this.activeTrials,
    required this.expiringThisWeek,
    required this.monthlySavings,
    required this.totalSavings,
    this.onActiveTrialsTap,
    this.onExpiringTap,
    this.onMonthlySavingsTap,
    this.onTotalSavingsTap,
  });

  @override
  Widget build(BuildContext context) {
    return StatsGrid(
      crossAxisCount: 2,
      cards: [
        StatsCard(
          title: 'Active Trials',
          value: activeTrials,
          icon: Icons.subscriptions,
          color: AppTheme.primaryGreen,
          onTap: onActiveTrialsTap,
        ),
        StatsCard(
          title: 'Expiring This Week',
          value: expiringThisWeek,
          icon: Icons.warning,
          color: AppTheme.warningOrange,
          onTap: onExpiringTap,
        ),
        StatsCard(
          title: 'Monthly Savings',
          value: '\$$monthlySavings',
          icon: Icons.savings,
          color: AppTheme.successGreen,
          onTap: onMonthlySavingsTap,
        ),
        StatsCard(
          title: 'Total Saved',
          value: '\$$totalSavings',
          icon: Icons.account_balance_wallet,
          color: AppTheme.celebrationGold,
          onTap: onTotalSavingsTap,
        ),
      ],
    );
  }
}
