import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'trial.g.dart';

@HiveType(typeId: 0)
class Trial extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String serviceName;

  @HiveField(2)
  late String serviceCategory;

  @HiveField(3)
  String? logoUrl;

  @HiveField(4)
  late DateTime startDate;

  @HiveField(5)
  late int trialDurationDays;

  @HiveField(6)
  late double monthlyCost;

  @HiveField(7)
  late String currency;

  @HiveField(8)
  late List<int> reminderDaysBeforeExpiry;

  @HiveField(9)
  late List<String> notificationTypes;

  @HiveField(10)
  late TrialStatus status;

  @HiveField(11)
  String? notes;

  @HiveField(12)
  DateTime? cancelledDate;

  @HiveField(13)
  double? moneySaved;

  @HiveField(14)
  DateTime createdAt;

  @HiveField(15)
  DateTime updatedAt;

  Trial({
    String? id,
    required this.serviceName,
    required this.serviceCategory,
    this.logoUrl,
    required this.startDate,
    required this.trialDurationDays,
    required this.monthlyCost,
    this.currency = 'USD',
    List<int>? reminderDaysBeforeExpiry,
    List<String>? notificationTypes,
    this.status = TrialStatus.active,
    this.notes,
    this.cancelledDate,
    this.moneySaved,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       reminderDaysBeforeExpiry = reminderDaysBeforeExpiry ?? [1, 3, 7],
       notificationTypes = notificationTypes ?? ['push'],
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  DateTime get expiryDate => startDate.add(Duration(days: trialDurationDays));

  int get daysRemaining {
    final now = DateTime.now();
    final expiry = expiryDate;
    return expiry.difference(now).inDays;
  }

  bool get isExpired => daysRemaining < 0;

  bool get isExpiringSoon => daysRemaining <= 7 && daysRemaining >= 0;

  bool get isExpiringToday => daysRemaining == 0;

  UrgencyLevel get urgencyLevel {
    if (isExpired || status != TrialStatus.active) return UrgencyLevel.none;
    if (daysRemaining <= 1) return UrgencyLevel.high;
    if (daysRemaining <= 3) return UrgencyLevel.medium;
    if (daysRemaining <= 7) return UrgencyLevel.low;
    return UrgencyLevel.none;
  }

  void cancel() {
    status = TrialStatus.cancelled;
    cancelledDate = DateTime.now();
    moneySaved = monthlyCost;
    updatedAt = DateTime.now();
  }

  void markAsConverted() {
    status = TrialStatus.converted;
    updatedAt = DateTime.now();
  }

  void markAsExpired() {
    status = TrialStatus.expired;
    updatedAt = DateTime.now();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'serviceName': serviceName,
      'serviceCategory': serviceCategory,
      'logoUrl': logoUrl,
      'startDate': startDate.toIso8601String(),
      'trialDurationDays': trialDurationDays,
      'monthlyCost': monthlyCost,
      'currency': currency,
      'reminderDaysBeforeExpiry': reminderDaysBeforeExpiry,
      'notificationTypes': notificationTypes,
      'status': status.name,
      'notes': notes,
      'cancelledDate': cancelledDate?.toIso8601String(),
      'moneySaved': moneySaved,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Trial.fromJson(Map<String, dynamic> json) {
    return Trial(
      id: json['id'],
      serviceName: json['serviceName'],
      serviceCategory: json['serviceCategory'],
      logoUrl: json['logoUrl'],
      startDate: DateTime.parse(json['startDate']),
      trialDurationDays: json['trialDurationDays'],
      monthlyCost: json['monthlyCost'].toDouble(),
      currency: json['currency'] ?? 'USD',
      reminderDaysBeforeExpiry: List<int>.from(json['reminderDaysBeforeExpiry'] ?? [1, 3, 7]),
      notificationTypes: List<String>.from(json['notificationTypes'] ?? ['push']),
      status: TrialStatus.values.firstWhere((e) => e.name == json['status']),
      notes: json['notes'],
      cancelledDate: json['cancelledDate'] != null ? DateTime.parse(json['cancelledDate']) : null,
      moneySaved: json['moneySaved']?.toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

@HiveType(typeId: 1)
enum TrialStatus {
  @HiveField(0)
  active,
  @HiveField(1)
  cancelled,
  @HiveField(2)
  expired,
  @HiveField(3)
  converted,
}

enum UrgencyLevel {
  none,
  low,
  medium,
  high,
}
