import 'package:hive/hive.dart';
import '../models/achievement.dart';
import '../models/trial.dart';
import 'notification_service.dart';

class AchievementService {
  static const String _achievementsBoxName = 'achievements';
  static const String _userStatsBoxName = 'user_stats';
  
  static Box<Achievement>? _achievementsBox;
  static Box<Map>? _userStatsBox;

  static Future<void> init() async {
    _achievementsBox = await Hive.openBox<Achievement>(_achievementsBoxName);
    _userStatsBox = await Hive.openBox<Map>(_userStatsBoxName);
    
    // Initialize default achievements if not already done
    if (_achievementsBox!.isEmpty) {
      await _initializeDefaultAchievements();
    }
  }

  static Future<void> _initializeDefaultAchievements() async {
    final defaultAchievements = AchievementData.getDefaultAchievements();
    for (final achievement in defaultAchievements) {
      await _achievementsBox!.put(achievement.id, achievement);
    }
  }

  static List<Achievement> getAllAchievements() {
    return _achievementsBox?.values.toList() ?? [];
  }

  static List<Achievement> getUnlockedAchievements() {
    return getAllAchievements().where((achievement) => achievement.isUnlocked).toList();
  }

  static List<Achievement> getLockedAchievements() {
    return getAllAchievements().where((achievement) => !achievement.isUnlocked).toList();
  }

  static List<Achievement> getAchievementsByCategory(AchievementCategory category) {
    return getAllAchievements().where((achievement) => achievement.category == category).toList();
  }

  static int getTotalPoints() {
    return getUnlockedAchievements().fold(0, (sum, achievement) => sum + achievement.rewardPoints);
  }

  static int getCurrentStreak() {
    final stats = _userStatsBox?.get('user_stats') as Map<String, dynamic>?;
    return stats?['current_streak'] ?? 0;
  }

  static Future<void> updateStreak() async {
    final stats = _userStatsBox?.get('user_stats') as Map<String, dynamic>? ?? {};
    final lastActiveDate = stats['last_active_date'] as String?;
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';
    
    if (lastActiveDate == null) {
      // First time using the app
      stats['current_streak'] = 1;
      stats['longest_streak'] = 1;
    } else {
      final yesterday = today.subtract(const Duration(days: 1));
      final yesterdayString = '${yesterday.year}-${yesterday.month}-${yesterday.day}';
      
      if (lastActiveDate == yesterdayString) {
        // Continuing streak
        stats['current_streak'] = (stats['current_streak'] ?? 0) + 1;
        stats['longest_streak'] = (stats['longest_streak'] ?? 0) < stats['current_streak'] 
            ? stats['current_streak'] 
            : stats['longest_streak'];
      } else if (lastActiveDate != todayString) {
        // Streak broken
        stats['current_streak'] = 1;
      }
      // If lastActiveDate == todayString, do nothing (already counted today)
    }
    
    stats['last_active_date'] = todayString;
    await _userStatsBox?.put('user_stats', stats);
    
    // Check streak achievements
    await _checkStreakAchievements(stats['current_streak']);
  }

  static Future<void> _checkStreakAchievements(int currentStreak) async {
    final streakAchievements = getAchievementsByCategory(AchievementCategory.streaks);
    
    for (final achievement in streakAchievements) {
      if (!achievement.isUnlocked && currentStreak >= achievement.targetValue) {
        await _unlockAchievement(achievement.id);
      }
    }
  }

  static Future<void> onTrialAdded(Trial trial) async {
    // Update trial count
    final stats = _userStatsBox?.get('user_stats') as Map<String, dynamic>? ?? {};
    stats['total_trials'] = (stats['total_trials'] ?? 0) + 1;
    await _userStatsBox?.put('user_stats', stats);
    
    // Check trial achievements
    await _checkTrialAchievements(stats['total_trials']);
    
    // Update streak
    await updateStreak();
  }

  static Future<void> onTrialCancelled(Trial trial) async {
    // Update savings
    final stats = _userStatsBox?.get('user_stats') as Map<String, dynamic>? ?? {};
    stats['total_savings'] = (stats['total_savings'] ?? 0.0) + trial.monthlyCost;
    stats['trials_cancelled'] = (stats['trials_cancelled'] ?? 0) + 1;
    await _userStatsBox?.put('user_stats', stats);
    
    // Check savings achievements
    await _checkSavingsAchievements(stats['total_savings']);
    
    // Check if this is the first save
    if (stats['trials_cancelled'] == 1) {
      await _unlockAchievement('first_save');
    }
    
    // Update streak
    await updateStreak();
  }

  static Future<void> onGoalAchieved() async {
    await _unlockAchievement('goal_achieved');
  }

  static Future<void> _checkTrialAchievements(int totalTrials) async {
    final trialAchievements = getAchievementsByCategory(AchievementCategory.trials);
    
    for (final achievement in trialAchievements) {
      if (!achievement.isUnlocked && totalTrials >= achievement.targetValue) {
        await _unlockAchievement(achievement.id);
      }
    }
  }

  static Future<void> _checkSavingsAchievements(double totalSavings) async {
    final savingsAchievements = getAchievementsByCategory(AchievementCategory.savings);
    
    for (final achievement in savingsAchievements) {
      if (!achievement.isUnlocked && totalSavings >= achievement.targetValue) {
        await _unlockAchievement(achievement.id);
      }
    }
  }

  static Future<void> _unlockAchievement(String achievementId) async {
    final achievement = _achievementsBox?.get(achievementId);
    if (achievement != null && !achievement.isUnlocked) {
      achievement.unlock();
      await _achievementsBox?.put(achievementId, achievement);
      
      // Show celebration notification
      await NotificationService.showImmediateNotification(
        title: '🏆 Achievement Unlocked!',
        body: '${achievement.title}: ${achievement.description}',
        payload: 'achievement:$achievementId',
      );
    }
  }

  static Map<String, dynamic> getUserStats() {
    return Map<String, dynamic>.from(_userStatsBox?.get('user_stats') ?? {});
  }

  static Future<void> resetAllAchievements() async {
    await _achievementsBox?.clear();
    await _userStatsBox?.clear();
    await _initializeDefaultAchievements();
  }

  static AchievementSummary getAchievementSummary() {
    final allAchievements = getAllAchievements();
    final unlockedAchievements = getUnlockedAchievements();
    final totalPoints = getTotalPoints();
    final currentStreak = getCurrentStreak();
    final stats = getUserStats();
    
    return AchievementSummary(
      totalAchievements: allAchievements.length,
      unlockedAchievements: unlockedAchievements.length,
      totalPoints: totalPoints,
      currentStreak: currentStreak,
      longestStreak: stats['longest_streak'] ?? 0,
      totalSavings: stats['total_savings']?.toDouble() ?? 0.0,
      totalTrials: stats['total_trials'] ?? 0,
      trialsCancelled: stats['trials_cancelled'] ?? 0,
    );
  }

  static List<Achievement> getRecentAchievements({int limit = 5}) {
    final unlockedAchievements = getUnlockedAchievements();
    unlockedAchievements.sort((a, b) => b.unlockedAt!.compareTo(a.unlockedAt!));
    return unlockedAchievements.take(limit).toList();
  }

  static double getCompletionPercentage() {
    final allAchievements = getAllAchievements();
    final unlockedAchievements = getUnlockedAchievements();
    
    if (allAchievements.isEmpty) return 0.0;
    return (unlockedAchievements.length / allAchievements.length) * 100;
  }
}

class AchievementSummary {
  final int totalAchievements;
  final int unlockedAchievements;
  final int totalPoints;
  final int currentStreak;
  final int longestStreak;
  final double totalSavings;
  final int totalTrials;
  final int trialsCancelled;

  AchievementSummary({
    required this.totalAchievements,
    required this.unlockedAchievements,
    required this.totalPoints,
    required this.currentStreak,
    required this.longestStreak,
    required this.totalSavings,
    required this.totalTrials,
    required this.trialsCancelled,
  });

  double get completionPercentage => 
      totalAchievements > 0 ? (unlockedAchievements / totalAchievements) * 100 : 0.0;
}
