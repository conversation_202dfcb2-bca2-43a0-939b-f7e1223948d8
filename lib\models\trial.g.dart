// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trial.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TrialAdapter extends TypeAdapter<Trial> {
  @override
  final int typeId = 0;

  @override
  Trial read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Trial(
      id: fields[0] as String?,
      serviceName: fields[1] as String,
      serviceCategory: fields[2] as String,
      logoUrl: fields[3] as String?,
      startDate: fields[4] as DateTime,
      trialDurationDays: fields[5] as int,
      monthlyCost: fields[6] as double,
      currency: fields[7] as String,
      reminderDaysBeforeExpiry: (fields[8] as List?)?.cast<int>(),
      notificationTypes: (fields[9] as List?)?.cast<String>(),
      status: fields[10] as TrialStatus,
      notes: fields[11] as String?,
      cancelledDate: fields[12] as DateTime?,
      moneySaved: fields[13] as double?,
      createdAt: fields[14] as DateTime?,
      updatedAt: fields[15] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, Trial obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.serviceName)
      ..writeByte(2)
      ..write(obj.serviceCategory)
      ..writeByte(3)
      ..write(obj.logoUrl)
      ..writeByte(4)
      ..write(obj.startDate)
      ..writeByte(5)
      ..write(obj.trialDurationDays)
      ..writeByte(6)
      ..write(obj.monthlyCost)
      ..writeByte(7)
      ..write(obj.currency)
      ..writeByte(8)
      ..write(obj.reminderDaysBeforeExpiry)
      ..writeByte(9)
      ..write(obj.notificationTypes)
      ..writeByte(10)
      ..write(obj.status)
      ..writeByte(11)
      ..write(obj.notes)
      ..writeByte(12)
      ..write(obj.cancelledDate)
      ..writeByte(13)
      ..write(obj.moneySaved)
      ..writeByte(14)
      ..write(obj.createdAt)
      ..writeByte(15)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TrialAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TrialStatusAdapter extends TypeAdapter<TrialStatus> {
  @override
  final int typeId = 1;

  @override
  TrialStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TrialStatus.active;
      case 1:
        return TrialStatus.cancelled;
      case 2:
        return TrialStatus.expired;
      case 3:
        return TrialStatus.converted;
      default:
        return TrialStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, TrialStatus obj) {
    switch (obj) {
      case TrialStatus.active:
        writer.writeByte(0);
        break;
      case TrialStatus.cancelled:
        writer.writeByte(1);
        break;
      case TrialStatus.expired:
        writer.writeByte(2);
        break;
      case TrialStatus.converted:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TrialStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
