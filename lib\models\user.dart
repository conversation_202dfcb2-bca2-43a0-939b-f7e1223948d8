import 'package:hive/hive.dart';

part 'user.g.dart';

@HiveType(typeId: 2)
class User extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String email;

  @HiveField(2)
  late String name;

  @HiveField(3)
  String? profilePictureUrl;

  @HiveField(4)
  late double monthlySavingsGoal;

  @HiveField(5)
  late String currency;

  @HiveField(6)
  late bool isEmailVerified;

  @HiveField(7)
  late DateTime createdAt;

  @HiveField(8)
  late DateTime updatedAt;

  @HiveField(9)
  late NotificationSettings notificationSettings;

  @HiveField(10)
  late AppSettings appSettings;

  User({
    required this.id,
    required this.email,
    required this.name,
    this.profilePictureUrl,
    this.monthlySavingsGoal = 100.0,
    this.currency = 'USD',
    this.isEmailVerified = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    NotificationSettings? notificationSettings,
    AppSettings? appSettings,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now(),
       notificationSettings = notificationSettings ?? NotificationSettings(),
       appSettings = appSettings ?? AppSettings();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'profilePictureUrl': profilePictureUrl,
      'monthlySavingsGoal': monthlySavingsGoal,
      'currency': currency,
      'isEmailVerified': isEmailVerified,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'notificationSettings': notificationSettings.toJson(),
      'appSettings': appSettings.toJson(),
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      profilePictureUrl: json['profilePictureUrl'],
      monthlySavingsGoal: json['monthlySavingsGoal']?.toDouble() ?? 100.0,
      currency: json['currency'] ?? 'USD',
      isEmailVerified: json['isEmailVerified'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      notificationSettings: NotificationSettings.fromJson(json['notificationSettings'] ?? {}),
      appSettings: AppSettings.fromJson(json['appSettings'] ?? {}),
    );
  }
}

@HiveType(typeId: 3)
class NotificationSettings extends HiveObject {
  @HiveField(0)
  late bool pushNotifications;

  @HiveField(1)
  late bool emailNotifications;

  @HiveField(2)
  late bool smsNotifications;

  @HiveField(3)
  late int quietHoursStart; // Hour in 24-hour format

  @HiveField(4)
  late int quietHoursEnd; // Hour in 24-hour format

  @HiveField(5)
  late bool weekendNotifications;

  NotificationSettings({
    this.pushNotifications = true,
    this.emailNotifications = true,
    this.smsNotifications = false,
    this.quietHoursStart = 22, // 10 PM
    this.quietHoursEnd = 8, // 8 AM
    this.weekendNotifications = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'pushNotifications': pushNotifications,
      'emailNotifications': emailNotifications,
      'smsNotifications': smsNotifications,
      'quietHoursStart': quietHoursStart,
      'quietHoursEnd': quietHoursEnd,
      'weekendNotifications': weekendNotifications,
    };
  }

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      pushNotifications: json['pushNotifications'] ?? true,
      emailNotifications: json['emailNotifications'] ?? true,
      smsNotifications: json['smsNotifications'] ?? false,
      quietHoursStart: json['quietHoursStart'] ?? 22,
      quietHoursEnd: json['quietHoursEnd'] ?? 8,
      weekendNotifications: json['weekendNotifications'] ?? true,
    );
  }
}

@HiveType(typeId: 4)
class AppSettings extends HiveObject {
  @HiveField(0)
  late bool darkMode;

  @HiveField(1)
  late String language;

  @HiveField(2)
  late bool biometricAuth;

  @HiveField(3)
  late bool autoBackup;

  AppSettings({
    this.darkMode = false,
    this.language = 'en',
    this.biometricAuth = false,
    this.autoBackup = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'darkMode': darkMode,
      'language': language,
      'biometricAuth': biometricAuth,
      'autoBackup': autoBackup,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      darkMode: json['darkMode'] ?? false,
      language: json['language'] ?? 'en',
      biometricAuth: json['biometricAuth'] ?? false,
      autoBackup: json['autoBackup'] ?? true,
    );
  }
}
