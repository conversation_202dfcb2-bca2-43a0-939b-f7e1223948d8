import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import '../models/trial.dart';
import '../models/user.dart';
import 'storage_service.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  static bool _initialized = false;

  static Future<void> init() async {
    if (_initialized) return;

    // Initialize timezone
    tz.initializeTimeZones();

    // Android initialization
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS initialization
    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _initialized = true;
  }

  static void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    // This could navigate to specific trial or open the app
    // TODO: Implement navigation to specific trial
  }

  static Future<bool> requestPermissions() async {
    final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
        _notifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

    bool? androidGranted;
    bool? exactAlarmGranted;

    if (androidImplementation != null) {
      androidGranted = await androidImplementation.requestNotificationsPermission();

      // Request exact alarm permission for Android 12+
      try {
        exactAlarmGranted = await androidImplementation.requestExactAlarmsPermission();
      } catch (e) {
        // If exact alarms are not supported, fall back to regular scheduling
        exactAlarmGranted = false;
      }
    }

    // For iOS, permissions are requested during initialization
    return (androidGranted ?? true) && (exactAlarmGranted ?? true);
  }

  static Future<bool> canScheduleExactAlarms() async {
    final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
        _notifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

    if (androidImplementation != null) {
      try {
        return await androidImplementation.canScheduleExactNotifications() ?? false;
      } catch (e) {
        return false;
      }
    }

    // For iOS, exact scheduling is always available
    return true;
  }

  static Future<void> scheduleTrialReminders(Trial trial) async {
    final user = StorageService.getCurrentUser();
    if (user == null || !user.notificationSettings.pushNotifications) return;

    // Cancel existing notifications for this trial
    await cancelTrialNotifications(trial.id);

    for (final daysBeforeExpiry in trial.reminderDaysBeforeExpiry) {
      await _scheduleTrialReminder(trial, daysBeforeExpiry);
    }
  }

  static Future<void> _scheduleTrialReminder(Trial trial, int daysBeforeExpiry) async {
    final user = StorageService.getCurrentUser();
    if (user == null) return;

    final reminderDate = trial.expiryDate.subtract(Duration(days: daysBeforeExpiry));
    final now = DateTime.now();

    // Don't schedule notifications for past dates
    if (reminderDate.isBefore(now)) return;

    // Check quiet hours
    final scheduledTime = _adjustForQuietHours(reminderDate, user.notificationSettings);

    final notificationId = _generateNotificationId(trial.id, daysBeforeExpiry);

    String title;
    String body;

    if (daysBeforeExpiry == 0) {
      title = '⏰ Trial Expires Today!';
      body = '${trial.serviceName} trial expires today. Cancel now to save \$${trial.monthlyCost.toStringAsFixed(2)}!';
    } else if (daysBeforeExpiry == 1) {
      title = '⚠️ Trial Expires Tomorrow!';
      body = '${trial.serviceName} trial expires in 1 day. Don\'t forget to cancel!';
    } else {
      title = '📅 Trial Reminder';
      body = '${trial.serviceName} trial expires in $daysBeforeExpiry days. Consider canceling to save \$${trial.monthlyCost.toStringAsFixed(2)}/month.';
    }

    const NotificationDetails notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        'trial_reminders',
        'Trial Reminders',
        channelDescription: 'Notifications for upcoming trial expirations',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      ),
      iOS: DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );

    // Check if exact alarms are available and use appropriate scheduling mode
    final canUseExactAlarms = await canScheduleExactAlarms();
    final scheduleMode = canUseExactAlarms
        ? AndroidScheduleMode.exactAllowWhileIdle
        : AndroidScheduleMode.inexact;

    try {
      await _notifications.zonedSchedule(
        notificationId,
        title,
        body,
        tz.TZDateTime.from(scheduledTime, tz.local),
        notificationDetails,
        payload: trial.id,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time,
        androidScheduleMode: scheduleMode,
      );
    } catch (e) {
      // If scheduling fails, try with inexact mode as fallback
      if (scheduleMode != AndroidScheduleMode.inexact) {
        await _notifications.zonedSchedule(
          notificationId,
          title,
          body,
          tz.TZDateTime.from(scheduledTime, tz.local),
          notificationDetails,
          payload: trial.id,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
          matchDateTimeComponents: DateTimeComponents.time,
          androidScheduleMode: AndroidScheduleMode.inexact,
        );
      } else {
        // Re-throw the error if inexact mode also fails
        rethrow;
      }
    }
  }

  static DateTime _adjustForQuietHours(DateTime originalTime, NotificationSettings settings) {
    final hour = originalTime.hour;
    
    // If the notification time falls within quiet hours, schedule for the end of quiet hours
    if (settings.quietHoursStart < settings.quietHoursEnd) {
      // Normal case: quiet hours don't cross midnight (e.g., 22:00 to 08:00)
      if (hour >= settings.quietHoursStart || hour < settings.quietHoursEnd) {
        return DateTime(
          originalTime.year,
          originalTime.month,
          originalTime.day,
          settings.quietHoursEnd,
          0,
        );
      }
    } else {
      // Quiet hours cross midnight (e.g., 22:00 to 08:00)
      if (hour >= settings.quietHoursStart && hour < settings.quietHoursEnd) {
        return DateTime(
          originalTime.year,
          originalTime.month,
          originalTime.day,
          settings.quietHoursEnd,
          0,
        );
      }
    }

    // Check weekend notifications
    if (!settings.weekendNotifications) {
      final weekday = originalTime.weekday;
      if (weekday == DateTime.saturday || weekday == DateTime.sunday) {
        // Schedule for Monday at the same time
        final daysToAdd = DateTime.monday - weekday + 7;
        return originalTime.add(Duration(days: daysToAdd));
      }
    }

    return originalTime;
  }

  static int _generateNotificationId(String trialId, int daysBeforeExpiry) {
    // Generate a unique notification ID based on trial ID and days before expiry
    return (trialId.hashCode + daysBeforeExpiry).abs() % 2147483647;
  }

  static Future<void> cancelTrialNotifications(String trialId) async {
    // Cancel all notifications for this trial
    for (int days = 0; days <= 30; days++) {
      final notificationId = _generateNotificationId(trialId, days);
      await _notifications.cancel(notificationId);
    }
  }

  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  static Future<void> showImmediateNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    const NotificationDetails notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        'immediate',
        'Immediate Notifications',
        channelDescription: 'Immediate notifications for app events',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      ),
      iOS: DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );

    await _notifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  static Future<void> scheduleAllActiveTrialReminders() async {
    final activeTrials = StorageService.getActiveTrials();
    
    for (final trial in activeTrials) {
      await scheduleTrialReminders(trial);
    }
  }

  static Future<void> updateNotificationSettings(NotificationSettings settings) async {
    if (!settings.pushNotifications) {
      // If push notifications are disabled, cancel all notifications
      await cancelAllNotifications();
    } else {
      // Reschedule all notifications with new settings
      await scheduleAllActiveTrialReminders();
    }
  }

  static Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  static Future<void> showTrialCancelledCelebration(Trial trial) async {
    await showImmediateNotification(
      title: '🎉 Great job!',
      body: 'You cancelled ${trial.serviceName} and saved \$${trial.monthlyCost.toStringAsFixed(2)}/month!',
      payload: trial.id,
    );
  }

  static Future<void> showSavingsGoalAchieved(double goalAmount) async {
    await showImmediateNotification(
      title: '🏆 Goal Achieved!',
      body: 'Congratulations! You\'ve saved \$${goalAmount.toStringAsFixed(2)} this month!',
    );
  }
}
